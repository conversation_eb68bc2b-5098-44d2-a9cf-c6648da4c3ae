# Technical Administration

This is a vaadin based administration gui which is currently targeted to allow
certain technical administrative tasks like showing the database table size. 
It might grow to a more advanced tool for configuration therefore taking some aspects
of configuration to improve those since we do not need to think about
api models too much and rather use java-reflection for setting up UI views.

Think for example about the db-based configurations which are now mostly strings expected to be in
a certain format. With vaading i could build more advanced UI for those based on the configuration classes which then
incorpates lists of model types or automatically detect enum types and show select boxes.

Most of the view will focus on technical tasks:
- Table monitors (incl. analyzing the tables)
- Overview of internal techn. State
  - archiving,externalizing
  - cleanup
  - achievements, sessions, scheduled stuff
- Reports
  - wallet reports
  - balance reports
  - user reports
- and with a 129,-/Month fee we could also include quite advanced charting

I will try to also add views for user search, simple actions on users

But a more helpful section of views would be feature monitors where each feature gets
a view helping to analyze usage, rewards, ...

