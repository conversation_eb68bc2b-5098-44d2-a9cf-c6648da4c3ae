buildscript {
    repositories {
        mavenCentral()
        maven { setUrl("https://maven.vaadin.com/vaadin-prereleases") }
        maven { setUrl("https://repo.spring.io/milestone") }
    }
}

plugins {
    id 'java'
    id 'org.springframework.boot' version "${springBootVersion}"
    id "com.gorylenko.gradle-git-properties" version "${gitPropertyPluginVersion}"
    id 'com.google.cloud.tools.jib'
    id 'com.vaadin'
}

apply plugin: 'io.spring.dependency-management'

//configurations {
//    compile.exclude module: "spring-boot-starter-tomcat"
//}


dependencyManagement {
    imports {
        mavenBom "com.vaadin:vaadin-bom:$vaadinVersion"
    }
}

repositories {
    mavenCentral()
    maven { setUrl("https://maven.vaadin.com/vaadin-prereleases") }
    maven { setUrl("https://repo.spring.io/milestone") }
    maven { setUrl("https://maven.vaadin.com/vaadin-addons") }
}

tasks.withType(Copy).configureEach {
    duplicatesStrategy 'warn'
}

springBoot {
    buildInfo()
}

bootRun {
    jvmArgs += ["--add-opens=java.base/java.time=ALL-UNNAMED", "--add-opens=java.base/sun.net=ALL-UNNAMED", "--add-opens=java.base/java.lang=ALL-UNNAMED", "--add-opens=java.base/java.math=ALL-UNNAMED", "--add-opens=java.base/java.util=ALL-UNNAMED", "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED", "--add-opens=java.base/java.net=ALL-UNNAMED", "--add-opens=java.base/java.text=ALL-UNNAMED", "--add-opens=java.sql/java.sql=ALL-UNNAMED"]
}

test {
    useJUnitPlatform()
    systemProperty 'junit.jupiter.extensions.autodetection.enabled', 'true'
    systemProperty 'flyway.enabled', 'false'
}
// LOG4J
configurations {
    developmentOnly
    runtimeClasspath {
        extendsFrom developmentOnly
    }
}
dependencies {
    runtimeOnly "org.springframework.boot:spring-boot-properties-migrator"
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation "com.vaadin:vaadin-spring-boot-starter"

    // vaadin addons
//    implementation 'org.vaadin.addons:filtering-grid:0.1.1'

    // components
    implementation project(':persistence')
    implementation project(':api')
    implementation project(':services')
    implementation project(':configuration')

    implementation project(':components:db-statistics')
    implementation project(':components:useraudit')
    implementation project(':components:localisation')
    implementation project(':components:authjwt-firebase')
    implementation project(':components:gamemanager')
    implementation project(':features:wheelspins')
    implementation project(':features:social-common')

    // aop
//    implementation "org.springframework.boot:spring-boot-starter-aop"

//    implementation "org.springframework.boot:spring-boot-starter-data-jpa"


}

compileJava.dependsOn.add('generateGitProperties')
compileJava.dependsOn.add('bootBuildInfo')
compileJava.inputs.files(processResources)

ext {
    // ...
    ext.getGitHash = { ->
        def stdout = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'rev-parse', '--short=8', 'HEAD'
            standardOutput = stdout
        }
        return stdout.toString().trim()
    }
}


jib {
    from {
        image = '666067033667.dkr.ecr.eu-central-1.amazonaws.com/sa-base:ac21.0.1.al2net'
        platforms {
            platform {
                architecture = 'amd64'
                os = 'linux'
            }
        }
        if (System.env['AWS_ECR_PW'] != null) {
            auth {
                username = 'AWS'
                password = "$System.env.AWS_ECR_PW"
            }
        }
    }
    to {
        image = '666067033667.dkr.ecr.eu-central-1.amazonaws.com/sa-admin'
        if (System.env['AWS_ECR_PW'] != null) {
            tags = ['latest', "${getGitHash()}"]
        } else {
            tags = ['latest', "LOCAL-${getGitHash()}"]
        }
        if (System.env['AWS_ECR_PW'] != null) {
            auth {
                username = 'AWS'
                password = "$System.env.AWS_ECR_PW"
            }
        }
    }
    container {
        appRoot = '/admin'
        jvmFlags = ["@/admin/resources/java-21-args", "-Xms786M", "-Xmx786M", "-XX:+UseZGC", "-XX:+ZGenerational", "-XX:+UseCompressedOops", "-XX:+UseStringDeduplication"]
        ports = ['8080']
        workingDirectory = '/admin'
    }
    allowInsecureRegistries = false
}
if (System.env['AWS_ECR_PW'] == null) {
    tasks.jib.dependsOn rootProject.loginToECR
}
