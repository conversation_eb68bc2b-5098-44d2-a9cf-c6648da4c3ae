plugins {
    id 'java'
    id 'org.springframework.boot' version "${springBootVersion}"
    id 'com.google.cloud.tools.jib'
    id "com.gorylenko.gradle-git-properties" version "${gitPropertyPluginVersion}"
}

apply plugin: 'io.spring.dependency-management'

springBoot {
    buildInfo()
}

configurations {
    compile.exclude module: "spring-boot-starter-tomcat"
}

bootRun {
    jvmArgs += ["--add-opens=java.base/java.time=ALL-UNNAMED","--add-opens=java.base/sun.net=ALL-UNNAMED", "--add-opens=java.base/java.lang=ALL-UNNAMED", "--add-opens=java.base/java.math=ALL-UNNAMED", "--add-opens=java.base/java.util=ALL-UNNAMED", "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED", "--add-opens=java.base/java.net=ALL-UNNAMED", "--add-opens=java.base/java.text=ALL-UNNAMED", "--add-opens=java.sql/java.sql=ALL-UNNAMED"]
}

test {
    useJUnitPlatform()
    systemProperty 'junit.jupiter.extensions.autodetection.enabled', 'true'
    systemProperty 'flyway.enabled', 'false'
}

dependencies {
    runtimeOnly "org.springframework.boot:spring-boot-properties-migrator"
    implementation 'org.springframework.boot:spring-boot-starter-log4j2'

    // subs
    implementation project(':persistence')
    implementation project(':api'), {
        exclude group: "com.adyen"
    }
    implementation project(':configuration')
    implementation project(':services')
    implementation project(':providers')
    implementation project(':bridges-shared')


    implementation project(':features:social-common')

//    implementation project(':features:ranking')
    implementation project(':features:missions')
    implementation project(':features:wheelspins')

    implementation project(':components:monitoring:monitoring-core')


    testImplementation project(path: ":test-util")

    implementation "net.javacrumbs.shedlock:shedlock-spring:${shedlockSpringVersion}"

    // monitoring
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation "io.micrometer:micrometer-registry-prometheus"
    implementation "commons-codec:commons-codec:1.15"

    // using jetty!
    implementation 'org.springframework.boot:spring-boot-starter-web-services', {
        exclude group: "org.springframework.boot", module: "spring-boot-starter-tomcat"
    }
    implementation 'org.springframework.boot:spring-boot-starter-jetty', {
        exclude group: "org.eclipse.jetty.ee10.websocket"
    }

    implementation 'org.springframework.boot:spring-boot-starter-security'

    implementation("com.github.gavlyukovskiy:datasource-proxy-spring-boot-starter:${datasourceProxyVersion}")

    testImplementation "commons-io:commons-io:2.11.0"
//    testImplementation "org.springframework.pulsar:spring-pulsar"

}

compileJava.dependsOn.add('generateGitProperties')
compileJava.dependsOn.add('bootBuildInfo')
compileJava.inputs.files(processResources)

ext {
    // ...
    ext.getGitHash = { ->
        def stdout = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'rev-parse', '--short=8', 'HEAD'
            standardOutput = stdout
        }
        return stdout.toString().trim()
    }
}

jib {
    from {
        image = '666067033667.dkr.ecr.eu-central-1.amazonaws.com/sa-base:ac21.0.1.al2net'
        platforms {
            platform {
                architecture = 'amd64'
                os = 'linux'
            }
        }
        if (System.env['AWS_ECR_PW'] != null) {
            auth {
                username = 'AWS'
                password = "$System.env.AWS_ECR_PW"
            }
        }
    }
    to {
        image = '666067033667.dkr.ecr.eu-central-1.amazonaws.com/sa-bridges'
        if (System.env['AWS_ECR_PW'] != null) {
            tags = ['latest', "${getGitHash()}"]
        } else {
            tags = ['latest', "LOCAL-${getGitHash()}"]
        }

        if (System.env['AWS_ECR_PW'] != null) {
            auth {
                username = 'AWS'
                password = "$System.env.AWS_ECR_PW"
            }
        }
    }
    container {
        appRoot = '/bridges'
        jvmFlags = ["@/bridges/resources/java-21-args", "-Xms1024M", "-Xmx1200M", "-XX:+UseZGC", "-XX:+ZGenerational", "-XX:+UseCompressedOops", "-XX:+UseStringDeduplication"]
        ports = ['8082']
        workingDirectory = '/bridges'
    }
    allowInsecureRegistries = false
}

if (System.env['AWS_ECR_PW'] == null) {
    tasks.jib.dependsOn rootProject.loginToECR
}