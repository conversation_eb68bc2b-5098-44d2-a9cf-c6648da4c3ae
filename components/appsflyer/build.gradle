plugins {
    id 'java-library'
}


repositories {
    mavenCentral()
}


dependencies {
    implementation project(':api')
    implementation project(':persistence')
    implementation 'org.apache.commons:commons-csv:1.11.0'
    implementation project(':configuration')
    implementation 'org.springframework:spring-web'
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
}

test {
    useJUnitPlatform()
}