plugins {
    id 'java-library'
}


repositories {
    mavenCentral()
}

dependencies {
    implementation project(':api')
    implementation project(':persistence')
    implementation 'org.springframework.boot:spring-boot'
    implementation 'org.springframework.boot:spring-boot-autoconfigure'
    implementation 'org.springframework:spring-web'
    implementation("com.google.firebase:firebase-admin:${firebaseVersion}")
    implementation 'io.hypersistence:hypersistence-utils-hibernate-62:3.4.3'

}

