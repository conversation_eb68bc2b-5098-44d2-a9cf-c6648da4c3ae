plugins {
    id 'java-library'
}

repositories {
    mavenCentral()
}

dependencies {
    implementation project(':api')
    implementation project(':persistence')

    implementation "org.kie:kie-ci:${droolsVersion}"
    implementation "org.drools:drools-decisiontables:${droolsVersion}"
    implementation "org.drools:drools-commands:${droolsVersion}"



    implementation "org.springframework.amqp:spring-rabbit"

    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
}

test {
    useJUnitPlatform()
}