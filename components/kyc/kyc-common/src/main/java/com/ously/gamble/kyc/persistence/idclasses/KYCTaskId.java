package com.ously.gamble.kyc.persistence.idclasses;

import java.io.Serializable;
import java.util.Objects;

public class KYCTaskId implements Serializable {

    Long userId;
    Long taskId;

    public KYCTaskId() {
    }

    public KYCTaskId(Long userId, Long taskId) {
        this.userId = userId;
        this.taskId = taskId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getTaskId() {
        return taskId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        var kycTaskId = (KYCTaskId) o;

        return Objects.equals(userId, kycTaskId.userId) && Objects.equals(taskId, kycTaskId.taskId);
    }

    @Override
    public int hashCode() {
        var result = userId != null ? userId.hashCode() : 0;
        result = 31 * result + (taskId != null ? taskId.hashCode() : 0);
        return result;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }
}
