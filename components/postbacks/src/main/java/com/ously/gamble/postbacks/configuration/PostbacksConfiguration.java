package com.ously.gamble.postbacks.configuration;

import com.ously.gamble.api.postbacks.PostbackAdapter;
import org.springframework.amqp.core.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
@ConditionalOnProperty(prefix = "postbacks", name = "enabled", havingValue = "true")
public class PostbacksConfiguration {


    @Bean
    DirectExchange postbacksExchange() {
        return ExchangeBuilder.directExchange("postbacks").durable(true).build();
    }

    @Bean
    Binding postbacksSendoutBinding() {
        return BindingBuilder.bind(postbacksSendoutQueue()).to(postbacksExchange()).withQueueName();
    }


    @Bean
    Queue postbacksSendoutQueue() {
        return QueueBuilder.durable(PostbackAdapter.POSTBACK_QUEUE_SENDOUT)
                .withArgument("x-dead-letter-exchange", "")
                .withArgument("x-dead-letter-routing-key",
                        postbacksSendoutQueueDLQ().getName())
                .build();
    }


    @Bean
    Queue postbacksSendoutQueueDLQ() {
        return QueueBuilder.durable(PostbackAdapter.POSTBACK_QUEUE_SENDOUT + "DLQ").build();
    }


}
