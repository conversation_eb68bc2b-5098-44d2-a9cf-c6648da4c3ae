package com.ously.gamble.postbacks.services;

import com.ously.gamble.api.postbacks.*;
import com.ously.gamble.exception.ResourceNotFoundException;
import com.ously.gamble.persistence.model.idclasses.PostbackReferralId;
import com.ously.gamble.postbacks.persistence.model.PostbackReferral;
import com.ously.gamble.postbacks.persistence.repository.PostbackReferralRepository;
import com.ously.gamble.postbacks.utils.PostbackDtoUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Base64;
import java.util.Optional;

@Service
@ConditionalOnProperty(prefix = "postbacks", name = "enabled", havingValue = "true")
public class PostbackReferralServiceImpl implements PostbackReferralService {

    final PostbackService postbackService;
    final PostbackReferralRepository postbackReferralRepository;

    public PostbackReferralServiceImpl(
            PostbackService postbackService,
            PostbackReferralRepository postbackReferralRepository) {
        this.postbackService = postbackService;
        this.postbackReferralRepository = postbackReferralRepository;
    }

    @Transactional
    @Override
    public String createPostbackReferral(CreatePostbackReferralPayload payload) {
        var ref = new PostbackReferral();
        var postback =
                postbackService.getPostbackByRoute(payload.route()).orElseThrow(() -> new ResourceNotFoundException("postback", "route", payload.route()));
        var num = postbackReferralRepository.getNextFreeNumForPostback(postback.id());
        ref.setPostbackId(postback.id());
        ref.setNum(num);
        ref.setSourceUrl(payload.sourceUrl());
        ref.setParams(payload.params());
        ref.setCountry("");
        ref.setIp("");
        ref.setRefId(0L);
        ref.setCreatedAt(Instant.now());

        postbackReferralRepository.save(ref);

        var registrationPayload = new RegistrationPostbackPayload(postback.id(), num);

        return encodeRegistrationPayload(registrationPayload);
    }

    @Transactional
    @Override
    public void addUserId(long postbackId, long num, long userId) {
        var referralOptional = postbackReferralRepository.findById(new PostbackReferralId(postbackId, num));
        if (referralOptional.isPresent()) {
            var ref = referralOptional.get();
            ref.setRefId(userId);
            postbackReferralRepository.save(ref);
        }
    }

    @Override
    public Optional<PostbackReferralDto> getPostbackReferral(Long postbackId, Long num) {
        var ref = postbackReferralRepository.findById(new PostbackReferralId(postbackId, num));
        return ref.map(PostbackDtoUtils::toPostbackReferralDto);

    }

    @Override
    public Optional<PostbackReferralDto> findByUserId(Long userId) {
        var ref = postbackReferralRepository.findByRefId(userId);

        return ref.map(PostbackDtoUtils::toPostbackReferralDto);
    }

    @Override
    public Optional<PostbackReferralDto> getPostbackReferralByCode(String code) {
        try {
            var payload = decodeRegistrationPayload(code);
            return getPostbackReferral(payload.postbackId(), payload.num());
        } catch (Exception ignored) {
        }
        return Optional.empty();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PostbackReferralDto> getAllPostbackReferrals(long postbackId, Pageable pageable) {
        var page = postbackReferralRepository.findAllByPostbackId(postbackId, pageable);
        var lg = page.getContent().stream().map(PostbackDtoUtils::toPostbackReferralDto).toList();
        return new PageImpl<>(lg, page.getPageable(), page.getTotalElements());
    }

    private String encodeRegistrationPayload(RegistrationPostbackPayload payload) {
        String str = String.valueOf(payload.postbackId()) + ':' + payload.num();
        return Base64.getEncoder().encodeToString(str.getBytes(StandardCharsets.UTF_8));
    }

    private RegistrationPostbackPayload decodeRegistrationPayload(String payload) {
        var json = new String(Base64.getDecoder().decode(payload));
        var split = json.split(":");
        var postbackId = split[0];
        var num = split[1];

        return new RegistrationPostbackPayload(Long.parseLong(postbackId), Long.parseLong(num));
    }
}
