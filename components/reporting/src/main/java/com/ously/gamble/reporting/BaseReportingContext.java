package com.ously.gamble.reporting;

import java.util.HashMap;
import java.util.Map;

public class BaseReportingContext {
    final Map<String, Object> objects = new HashMap<>();

    public void addObject(String name, Object value) {
        objects.put(name, value);
    }

    public Object getObject(String name, Object defaultValue) {
        return objects.getOrDefault(name, defaultValue);
    }


}
