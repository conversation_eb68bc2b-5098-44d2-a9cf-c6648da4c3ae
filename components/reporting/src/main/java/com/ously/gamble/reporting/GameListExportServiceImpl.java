package com.ously.gamble.reporting;

import com.ously.gamble.api.games.CasinoGameImage;
import com.ously.gamble.api.games.GameImageService;
import com.ously.gamble.api.games.GameManagementService;
import com.ously.gamble.api.reporting.GameListExportService;
import com.ously.gamble.api.statistics.StatisticsService;
import com.ously.gamble.api.vendor.VendorManagementService;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@ConditionalOnOffloader
public class GameListExportServiceImpl extends BaseReportingService implements GameListExportService {

    private final Logger log = LoggerFactory.getLogger(GameListExportServiceImpl.class);

    private final VendorManagementService vMgmt;
    private final GameManagementService gMgmt;
    private final GameImageService giSrv;
    private final StatisticsService statsSrv;


    public GameListExportServiceImpl(VendorManagementService vMgmt, GameManagementService gMgmt,
                                     GameImageService giSrv, StatisticsService statSrv) {
        this.vMgmt = vMgmt;
        this.gMgmt = gMgmt;
        this.giSrv = giSrv;
        this.statsSrv = statSrv;
    }

    @Transactional(timeout = 180)
    @Override
    public byte[] gamelistExport(int statDays) throws Exception {
        log.info("Start creating gamelist");

        var wb = createWorkbookFromResource("/reports/gamelist.xlsx");
        var from = LocalDateTime.now(ZoneOffset.UTC).minusDays(statDays);
        var to = LocalDateTime.now(ZoneOffset.UTC);
        // now update totals
        updateProviders(wb);
        updateStatistics(wb, from, to);
        updateGames(wb);
        var bytes = writeWorkbookToByteArray(wb);
        log.info("Done creating gamelist with {} days of stats", statDays);
        return bytes;

    }

    private void updateStatistics(Workbook wb, LocalDateTime from, LocalDateTime to) {
        var sheet = wb.getSheet("Statistics");
        var all = statsSrv.getGameRtps(from, to);
        var cRow = 1;
        for (var csp : all.getValue()) {
            // Game Id	Sessions	Bets	Wins	Rounds
            setCellValueNumeric(sheet, cRow, 0, csp.getGameId());
            setCellValueNumeric(sheet, cRow, 1, csp.getSessions());
            setCellValueNumeric(sheet, cRow, 2, BigDecimal.valueOf(csp.getSumBets()));
            setCellValueNumeric(sheet, cRow, 3, BigDecimal.valueOf(csp.getSumWins()));
            setCellValueNumeric(sheet, cRow, 4, csp.getSpins());
            setCellValueNumeric(sheet, cRow, 5, BigDecimal.valueOf(csp.getRtp()));
            cRow++;
        }
        sheet.setForceFormulaRecalculation(true);
    }


    private void updateProviders(Workbook wb) {
        var sheet = wb.getSheet("Providers");
        var all = vMgmt.findAll();
        var cRow = 1;
        for (var csp : all) {
            setCellValueNumeric(sheet, cRow, 0, csp.getId().longValue());
            setCellValueString(sheet, cRow, 1, csp.getProviderName());
            setCellValueString(sheet, cRow, 2, csp.getBridgeName());
            setCellValueNumeric(sheet, cRow, 3, csp.getActive() ? 1 : 0);
            setCellValueString(sheet, cRow, 4, csp.getProviderHomepage());
            setCellValueString(sheet, cRow, 5, csp.getBlockedCountries());
            cRow++;
        }
        sheet.setForceFormulaRecalculation(true);
    }


    private void updateGames(Workbook wb) {
        var sheet = wb.getSheet("Games");
        var all = gMgmt.findAll();
        var imgSet =
                giSrv.getGameImagesNoData().stream().map(CasinoGameImage::getGameId).collect(Collectors.toSet());
        var cRow = 2;
        var pFormula = getCellFormula(sheet, 2, 4);

        var s1Formula = getCellFormula(sheet, 2, 15);
        var s2Formula = getCellFormula(sheet, 2, 16);
        var s3Formula = getCellFormula(sheet, 2, 17);
        var s4Formula = getCellFormula(sheet, 2, 18);
        var s5Formula = getCellFormula(sheet, 2, 19);


        for (var g : all) {
            setCellValueNumeric(sheet, cRow, 0, g.getId());
            setCellValueString(sheet, cRow, 1, g.getName());
            setCellValueString(sheet, cRow, 2, g.getGameId());
            setCellValueNumeric(sheet, cRow, 3, g.getVendor().getId().longValue());
            setCellValueFormula(sheet, cRow, 4, pFormula.replace("(D3", "(D" + (cRow + 1)));
            setCellValueNumeric(sheet, cRow, 5, g.getActive() ? 1 : 0);
            setCellValueNumeric(sheet, cRow, 6, g.getIos() ? 1 : 0);
            setCellValueNumeric(sheet, cRow, 7, g.getAndroid() ? 1 : 0);
            setCellValueNumeric(sheet, cRow, 8, g.getDesktop() ? 1 : 0);

            // Thumb?
            setCellValueNumeric(sheet, cRow, 9, (imgSet.contains(g.getId())) ? 1 : 0);
            setCellValueNumeric(sheet, cRow, 10, (g.getGameInfo() != null) ? 1 : 0);

            // Attributes Rtp	Order	Level	Tags
            setCellValueNumeric(sheet, cRow, 11, g.getRtp());
            setCellValueNumeric(sheet, cRow, 12, Objects.requireNonNullElse(g.getSortOrder(),
                    99999));
            setCellValueNumeric(sheet, cRow, 13, g.getLevel());
            setCellValueString(sheet, cRow, 14, "");

            // Stats (formula update)
            setCellValueFormula(sheet, cRow, 15, s1Formula.replace("($A3", "($A" + (cRow + 1)));
            setCellValueFormula(sheet, cRow, 16, s2Formula.replace("($A3", "($A" + (cRow + 1)));
            setCellValueFormula(sheet, cRow, 17, s3Formula.replace("($A3", "($A" + (cRow + 1)));
            setCellValueFormula(sheet, cRow, 18, s4Formula.replace("($A3", "($A" + (cRow + 1)));
            setCellValueFormula(sheet, cRow, 19, s5Formula.replace("($A3", "($A" + (cRow + 1)));


            // Slotcatalog status
            // sc-id	sc-name	sc.provider	sc.cateegories	sc.rtp	sc.order

            if (g.getGameInfo() != null) {
                var gi = g.getGameInfo();
                setCellValueNumeric(sheet, cRow, 20, gi.getId());
                setCellValueString(sheet, cRow, 21, gi.getGameName());
                setCellValueString(sheet, cRow, 22, gi.getVendor());
                setCellValueString(sheet, cRow, 23, "");
                setCellValueNumeric(sheet, cRow, 24, gi.getRtp());
                setCellValueNumeric(sheet, cRow, 25, gi.getSlotRank());
            }
            cRow++;
        }
        sheet.setForceFormulaRecalculation(true);
    }

}
