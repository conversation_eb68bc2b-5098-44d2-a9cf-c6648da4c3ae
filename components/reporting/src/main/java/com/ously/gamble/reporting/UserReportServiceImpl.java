package com.ously.gamble.reporting;

import com.ously.gamble.api.PurchaseManagementService;
import com.ously.gamble.api.achievements.Reward;
import com.ously.gamble.api.reporting.UserReportService;
import com.ously.gamble.api.session.SessionService;
import com.ously.gamble.api.statistics.StatisticsService;
import com.ously.gamble.api.user.UserAdminService;
import com.ously.gamble.api.user.UserService;
import com.ously.gamble.api.user.audit.UserAuditInfoEntry;
import com.ously.gamble.api.user.audit.UserAuditService;
import com.ously.gamble.api.user.exclusion.AdminExclusionService;
import com.ously.gamble.api.user.exclusion.UserExclusionDto;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.persistence.projections.BalanceStatsPJ;
import com.ously.gamble.persistence.repository.WalletRepository;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ZERO;

@Service
@ConditionalOnOffloader
public class UserReportServiceImpl extends BaseReportingService implements UserReportService {
    private static final int SESSION_MAXCOUNT = 500;
    private static final int DEPOSITS_MAXCOUNT = 500;
    private static final int MAX_ACHIEVEMENT_COUNT = 1000;
    private final Logger log = LoggerFactory.getLogger(UserReportServiceImpl.class);

    private final UserAdminService userAdminService;

    private final SessionService sessionService;

    private final PurchaseManagementService purchService;

    private final WalletRepository wRepo;

    private final UserService userService;

    private final StatisticsService statService;

    private final UserAuditService uAuditSrv;

    private final AdminExclusionService uaExclService;


    public UserReportServiceImpl(
                                 UserAdminService uAdmService,
                                 UserService uSrv,
                                 SessionService ssSrv,
                                 Optional<PurchaseManagementService> puSrv,
                                 WalletRepository wRepo,
                                 StatisticsService statSrv,
                                 Optional<UserAuditService> auditService,
                                 Optional<AdminExclusionService> uaExcSOpt

    ) {
        this.userAdminService = uAdmService;
        this.userService = uSrv;
        this.sessionService = ssSrv;
        this.purchService = puSrv.orElse(null);
        this.wRepo = wRepo;
        this.statService = statSrv;
        this.uAuditSrv = auditService.orElse(null);
        this.uaExclService = uaExcSOpt.orElse(null);
    }

    @Override
    public byte[] createUserReport(long userId) throws Exception {
        log.info("Start creating userReport");
        var wb = createWorkbookFromResource("/reports/userReport.xlsx");


        // Common sheets
        updateUserInfo(wb, userId);
        updateSessions(wb, userId, SESSION_MAXCOUNT);
        updateBalanceHistory(wb, userId, 90);
        updateAuditEntries(wb, userId);
        updateUserExclusionEntries(wb, userId);

        // Social sheets
            updatePurchases(wb, userId);
            updateAchievements(wb, userId);
            updateTokens(wb, userId);
            removeSheets(wb, "Deposits", "Payouts", "Rakebacks", "Bonus", "AffCampaign");

        var bytes = writeWorkbookToByteArray(wb);
        log.info("Done creating userReport for user {}", userId);
        return bytes;
    }


    private void updateUserInfo(Workbook wb, long userId) {
        var sheet = wb.getSheet("UserInformation");

        var dtStyle = getDatetimeCellStyle(wb);

        var uv = userAdminService.getUsersViewForId(userId);

        setCellValueNumeric(sheet, 1, 2, uv.getId());
        setCellValueString(sheet, 2, 2, uv.getEmail());
        setCellValueString(sheet, 3, 2, uv.getDisplayName());
        setCellValueInstant(sheet, 4, 2, uv.getCreatedAt(), dtStyle);
        setCellValueString(sheet, 5, 2, uv.getLocalId());
        setCellValueInstant(sheet, 6, 2, uv.getFirstPurchase(), dtStyle);

        setCellValueString(sheet, 8, 2, uv.getName());
        setCellValueString(sheet, 9, 2, uv.getSurname());
        setCellValueString(sheet, 10, 2, (uv.isSystemUser()) ? "ADMIN" : "USER");

        setCellValueString(sheet, 12, 2, uv.getTags());
        setCellValueNumeric(sheet, 14, 2, uv.getLevel());
        setCellValueNumeric(sheet, 15, 2, uv.getSumBet());
        setCellValueNumeric(sheet, 16, 2, uv.getSumWin());
        setCellValueNumeric(sheet, 17, 2, uv.getSessionCount());

        var w = wRepo.getWalletById(userId);

        setCellValueNumeric(sheet, 2, 4, w.getBalance());
        setCellValueNumeric(sheet, 2, 5, ZERO);
        setCellValueNumeric(sheet, 3, 4, ZERO);
        setCellValueNumeric(sheet, 3, 5, ZERO);

            // write Purchase Info
            setCellValueNumeric(sheet, 8, 4, uv.getCountPurchases());
            setCellValueNumeric(sheet, 8, 5, uv.getSumPurchases());
    }

    private void updateUserExclusionEntries(Workbook wb, long userId) {

        if (uaExclService == null) {
            removeSheets(wb, "Exclusions");
            return;
        }

        // Exclusions
        var sheet = wb.getSheet("Exclusions");
        var dtStyle = getDatetimeCellStyle(wb);

        var row = 1;
        log.info("Reading Exclusions");
        List<UserExclusionDto> exclusionsForUser = uaExclService.getExclusionsForUser(userId);

        for (var ue : exclusionsForUser) {
            setCellValueNumeric(sheet, row, 0, ue.getNum());
            setCellValueInstant(sheet, row, 1, ue.getCreatedAt(), dtStyle);
            if (ue.getCreatedBy() != null) {
                setCellValueNumeric(sheet, row, 2, ue.getCreatedBy());
            }
            setCellValueString(sheet, row, 3, ue.getType().name());
            setCellValueInstant(sheet, row, 4, ue.getExpiresAt(), dtStyle);
            setCellValueString(sheet, row, 5, ue.getReason());
            setCellValueInstant(sheet, row, 6, ue.getRemovedAt(), dtStyle);
            setCellValueNumeric(sheet, row, 7, ue.getRemovedBy());
            if (ue.getRemovalType() != null) {
                setCellValueString(sheet, row, 8, ue.getRemovalType().name());
            }
            row++;
        }
    }

    private void updateAuditEntries(Workbook wb, long userId) {
        if (uAuditSrv == null) {
            removeSheets(wb, "UserAudit");
            return;
        }

        var sheet = wb.getSheet("UserAudit");

        var dtStyle = getDatetimeCellStyle(wb);

        var row = 1;
        log.info("Reading AuditEntries");
        var allAuditDtos = uAuditSrv.getAllUserAuditEntries(userId, 0, 1000);

        for (var ae : allAuditDtos) {
            setCellValueNumeric(sheet, row, 0, ae.getNum());
            setCellValueInstant(sheet, row, 1, ae.getCreatedAt(), dtStyle);
            if (ae.getCreatedBy() != null) {
                setCellValueNumeric(sheet, row, 2, ae.getCreatedBy());
            }
            setCellValueString(sheet, row, 3, ae.getType().name());
            setCellValueNumeric(sheet, row, 4, ae.getImportance());
            setCellValueString(sheet, row, 5, ae.getDescription());

            // Get infos
            String collect = ae.getInfos().entries().stream().map(UserAuditInfoEntry::toString).collect(Collectors.joining());
            setCellValueString(sheet, row, 6, collect);

            row++;
        }
    }


    private void updateAchievements(Workbook wb, long userId) {
        var sheet = wb.getSheet("Achievements");

        var dtStyle = getDatetimeCellStyle(wb);

        var row = 1;
        log.info("Reading Achievements");
        var allAchievements = userService.getAllAchievements(userId);

        for (var sE : allAchievements) {
            setCellValueInstant(sheet, row, 0, sE.getCreatedAt(), dtStyle);
            setCellValueNumeric(sheet, row, 1, sE.getId());
            setCellValueString(sheet, row, 2, sE.getType().name());
            setCellValueString(sheet, row, 3, (sE.getClaimed()) ? "CLAIMED" : "UNCLAIMED");
            setCellValueString(sheet, row, 4, sE.getQualifier());
            setCellValueString(sheet, row, 5, createStringFromMap(sE.getVariables()));
            setCellValueString(sheet, row, 6, createStringFromRewards(sE.getRewards()));
            row++;
            if (row > MAX_ACHIEVEMENT_COUNT) {
                break;
            }
        }
    }

    private void updateTokens(Workbook wb, long userId) {
        var sheet = wb.getSheet("Tokens");

        var row = 1;
        log.info("Reading Tokens");
        var allTokens = userService.getAvailableTokens(userId);

        for (var sE : allTokens) {
            setCellValueString(sheet, row, 0, sE.getType().name());
            setCellValueNumeric(sheet, row, 1, sE.getCount());
            setCellValueNumeric(sheet, row, 2, sE.getBoost());
            setCellValueNumeric(sheet, row, 3, sE.getDurationInMinutes());
            row++;
        }
    }

    private String createStringFromRewards(List<Reward> rewards) {
        if (rewards == null || rewards.isEmpty()) {
            return "";
        }
        return rewards.stream().map(Reward::getStringDefinition).collect(Collectors.joining(","));
    }

    private String createStringFromMap(Map<String, String> variables) {
        if (variables == null || variables.isEmpty()) {
            return "";
        }
        return variables.entrySet().stream().map(a -> a.getKey() + "=" + a.getValue()).collect(Collectors.joining(","));
    }







    private void updatePurchases(Workbook wb, long userId) {
        var sheet = wb.getSheet("Purchases");

        var dtStyle = getDatetimeCellStyle(wb);

        var page = 0;
        var row = 1;
        while (row < (DEPOSITS_MAXCOUNT + 1)) {
            log.info("Reading page {} from Purchases", page);
            Pageable pg = PageRequest.of(page, 50);
            var allPurchases = purchService.findByUserIdOrderByCreatedAtDesc(userId, pg);

            if (allPurchases.getContent().isEmpty()) {
                break;
            }
            for (var sE : allPurchases.getContent()) {
                setCellValueInstant(sheet, row, 0, sE.getCreatedAt(), dtStyle);
                setCellValueString(sheet, row, 1, sE.getStatus().name());
                setCellValueNumeric(sheet, row, 2, sE.getCost());
                setCellValueString(sheet, row, 3, sE.getPaymentMethod());
                setCellValueString(sheet, row, 4, sE.getPsp());
                setCellValueString(sheet, row, 5, sE.getOrderRef());
                setCellValueString(sheet, row, 6, sE.getPlatform().name());
                setCellValueNumeric(sheet, row, 7, sE.getConsumableId());
                setCellValueString(sheet, row, 8, sE.getAppliedItems());
                row++;
            }
            page++;
        }
    }



    private void updateSessions(Workbook wb, long userId, int maxCount) {
        var sheet = wb.getSheet("Sessions");
        var dtStyle = getDatetimeCellStyle(wb);

        var page = 0;
        var row = 1;
        while (row < (maxCount + 1)) {
            log.info("Reading page {} from sessionStats", page);
            var statisticsForUser = sessionService.getStatisticsForUser(userId, page, 100);
            if (statisticsForUser.getContent().isEmpty()) {
                break;
            }
            for (var sE : statisticsForUser.getContent()) {
                setCellValueInstant(sheet, row, 0, sE.getStartAt(), dtStyle);

                setCellValueNumeric(sheet, row, 1, sE.getGameId());
                setCellValueNumeric(sheet, row, 2, sE.getNumSpins());
                setCellValueNumeric(sheet, row, 3, sE.getSumBet());
                setCellValueNumeric(sheet, row, 4, sE.getSumWin());
                setCellValueNumeric(sheet, row, 5, sE.getSumWin().subtract(sE.getSumBet()));
                setCellValueNumeric(sheet, row, 6, (sE.getEndAt().getEpochSecond() - sE.getStartAt().getEpochSecond()) / 60);
                setCellValueNumeric(sheet, row, 7, sE.getMaxBet());
                setCellValueNumeric(sheet, row, 8, sE.getMaxWin());
                row++;
            }
            page++;
        }
    }


    private void updateBalanceHistory(Workbook wb, long userId, int daysBack) {
        var sheet = wb.getSheet("BalanceHistory");

        var dtStyle = getDateCellStyle(wb);

        List<BalanceStatsPJ> balanceStatistics = statService.getBalanceStatistics(userId, daysBack);

        if (balanceStatistics.isEmpty()) {
            return;
        }

        Map<LocalDate, BalanceStatsPJ> entryMap = balanceStatistics.stream().collect(Collectors.toMap(
                a -> LocalDate.parse(a.getRdate()), a -> a
        ));

        BalanceStatsPJ actE = balanceStatistics.getFirst();
        String cDateStr = actE.getRdate();
        var actDate = LocalDate.parse(cDateStr);
        var todayDate = LocalDate.now();

        int row = 1;
        boolean clone = false;
        while (!actDate.isAfter(todayDate)) {

            setCellValueDate(sheet, row, 0, actDate, dtStyle);

            if (!clone) {
                setCellValueNumeric(sheet, row, 1, actE.getMinBal());
                setCellValueNumeric(sheet, row, 2, actE.getMaxBal());
                setCellValueNumeric(sheet, row, 3, actE.getDepos());
                setCellValueNumeric(sheet, row, 4, actE.getPouts());
            } else {
                setCellValueNumeric(sheet, row, 1, actE.getMaxBal());
                setCellValueNumeric(sheet, row, 2, actE.getMaxBal());
                setCellValueNumeric(sheet, row, 3, 0);
                setCellValueNumeric(sheet, row, 4, 0);
            }
            actDate = actDate.plusDays(1);
            if (entryMap.containsKey(actDate)) {
                actE = entryMap.get(actDate);
                clone = false;
            } else {
                clone = true;
            }
            row++;
        }
    }

}
