package com.ously.gamble.reporting.modules;

import com.ously.gamble.api.features.SlotConfiguration;
import com.ously.gamble.api.features.WheelService;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.conditions.ConditionalOnSocial;
import com.ously.gamble.reporting.BaseReportingContext;
import com.ously.gamble.reporting.BaseReportingService;
import com.ously.gamble.reporting.ReportModule;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

@Component
@ConditionalOnOffloader
@ConditionalOnSocial
public class WheelspinReportModule extends BaseReportingService implements ReportModule {

    private final WheelService wheelService;

    public WheelspinReportModule(WheelService wheelService) {
        this.wheelService = wheelService;
    }


    @Override
    @Transactional
    public void updateSheet(Workbook wb, BaseReportingContext ctx, Instant from, Instant to) {
        updateWheelSheet(wb, ctx, from, to);
    }

    private void updateWheelSheet(Workbook wb, BaseReportingContext ctx, Instant from, Instant to) {

        var sheet = wb.getSheet("Wheelspins");

        // List wheelspin configs for levels
        // including the statistical result taking the min/medium/max distribution into account

        var row = 5;
        for (int level = 0; level < 501; level++) {
            var slotConfigurationForLevel = wheelService.getSlotConfigurationForLevel(level);
            setCellValueNumeric(sheet, row, 0, level);
            setCellValueNumeric(sheet, row, 1, slotConfigurationForLevel.getMinValue());
            setCellValueNumeric(sheet, row, 2, slotConfigurationForLevel.getMedValue());
            setCellValueNumeric(sheet, row, 3, slotConfigurationForLevel.getMaxValue());

            setCellValueNumeric(sheet, row, 4, getMeanValue(slotConfigurationForLevel));
            row++;
        }

    }

    private long getMeanValue(SlotConfiguration slConfig) {
        int countTypes = slConfig.getTypes().length;
        long sumWin = 0L;
        for (var type : slConfig.getTypes()) {
            switch (type) {
                case MEDIUMCOIN -> sumWin += slConfig.getMedValue();
                case MINIMUMCOIN -> sumWin += slConfig.getMinValue();
                case MAXIMUMCOIN -> sumWin += slConfig.getMaxValue();
            }
        }
        return sumWin / countTypes;
    }

}
