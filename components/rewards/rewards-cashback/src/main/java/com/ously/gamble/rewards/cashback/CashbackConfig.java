package com.ously.gamble.rewards.cashback;

import jakarta.annotation.PostConstruct;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

@ConfigurationProperties(prefix = "rewards.cashback")
@Component
public class CashbackConfig {

    /**
     * if true, entries are saved/updated, if false only log entries are generated
     */
    private boolean enabled;
    private boolean createPromotion;

    private final Map<String, BigDecimal> depositfactors = new HashMap<>();

    private BigDecimal zeroOut = new BigDecimal("0.20");

    private Integer wagerFactor = Integer.valueOf(15);

    private BigDecimal maxCashout = new BigDecimal(2000);

    private Integer cashoutFactor = 10;

    private Integer bonusExpiryHours = Integer.valueOf(24);

    TreeMap<BigDecimal, BigDecimal> depFactorMap;


    public Integer getCashoutFactor() {
        return cashoutFactor;
    }

    public void setCashoutFactor(Integer cashoutFactor) {
        this.cashoutFactor = cashoutFactor;
    }

    public Integer getBonusExpiryHours() {
        return bonusExpiryHours;
    }

    public void setBonusExpiryHours(Integer bonusExpiryHours) {
        this.bonusExpiryHours = bonusExpiryHours;
    }

    public void setZeroOut(BigDecimal zeroOut) {
        this.zeroOut = zeroOut;
    }

    public void setWagerFactor(Integer wagerFactor) {
        this.wagerFactor = wagerFactor;
    }

    public void setMaxCashout(BigDecimal maxCashout) {
        this.maxCashout = maxCashout;
    }

    public boolean isCreatePromotion() {
        return createPromotion;
    }

    public void setCreatePromotion(boolean createPromotion) {
        this.createPromotion = createPromotion;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public BigDecimal getZeroOut() {
        return zeroOut;
    }

    public Integer getWagerFactor() {
        return wagerFactor;
    }

    public BigDecimal getMaxCashout() {
        return maxCashout;
    }

    public Map<String, BigDecimal> getDepositfactors() {
        return depositfactors;
    }

    @PostConstruct
    public void createFastLookup() {
        depFactorMap = new TreeMap<>();
        for (var dpEntry : depositfactors.entrySet()) {
            depFactorMap.put(new BigDecimal(dpEntry.getKey()), dpEntry.getValue());
        }
    }


    public BigDecimal getCashbackFactorForDepositAmount(BigDecimal depAmount) {
        BigDecimal key = depFactorMap.floorKey(depAmount);
        if (key == null) {
            return BigDecimal.ZERO;
        }
        return depFactorMap.get(key);
    }

}
