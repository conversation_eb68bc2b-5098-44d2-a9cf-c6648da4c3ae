package com.ously.gamble.rewards.cashback.config;

import com.ously.gamble.api.cache.CachedMap;
import com.ously.gamble.api.cache.CachedMapFactory;
import com.ously.gamble.api.cache.CodecType;
import com.ously.gamble.api.rewards.ActiveCashback;
import org.springframework.amqp.core.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
@ConditionalOnProperty(prefix = "rewards.cashback", name = "enabled", havingValue = "true")
public class CashbackConfiguration {

    public static final String CASHBACK_CREATE_QUEUE = "cashback.new";
    public static final String CASHBACK_EVAL_QUEUE = "cashback.eval";

    @Bean
    CachedMap<Long, ActiveCashback> userCashbackMap(
            CachedMapFactory<Long, ActiveCashback> fact) {
        return fact.createLocalCachedMap("rewardsCashback", CodecType.FST, Long.class, ActiveCashback.class, 60 * 5);
    }


    @Bean
    DirectExchange rewardsExchange() {
        return ExchangeBuilder.directExchange("rewards").durable(true).build();
    }

    @Bean
    Binding cashbackCreateQueueBinding() {
        return BindingBuilder.bind(cashbackCreateQueue()).to(rewardsExchange()).withQueueName();
    }

    @Bean
    Binding cashbackEvalQueueBinding() {
        return BindingBuilder.bind(cashbackEvalQueue()).to(rewardsExchange()).withQueueName();
    }

    @Bean
    Queue cashbackCreateQueue() {
        return QueueBuilder.durable(CASHBACK_CREATE_QUEUE)
                .withArgument("x-dead-letter-exchange", "")
                .withArgument("x-dead-letter-routing-key", CASHBACK_CREATE_QUEUE + "_DLQ")
                .build();
    }

    @Bean
    Queue cashbackEvalQueue() {
        return QueueBuilder.durable(CASHBACK_EVAL_QUEUE)
                .withArgument("x-dead-letter-exchange", "")
                .withArgument("x-dead-letter-routing-key", CASHBACK_EVAL_QUEUE + "_DLQ")
                .build();
    }

    @Bean
    Queue cashbackCreateQueueDLQ() {
        return QueueBuilder.durable(CASHBACK_CREATE_QUEUE + "_DLQ").build();
    }

    @Bean
    Queue cashbackEvalQueueDLQ() {
        return QueueBuilder.durable(CASHBACK_EVAL_QUEUE + "_DLQ").build();
    }


}
