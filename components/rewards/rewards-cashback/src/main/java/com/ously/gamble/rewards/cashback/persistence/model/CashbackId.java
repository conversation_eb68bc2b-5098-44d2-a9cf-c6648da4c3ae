package com.ously.gamble.rewards.cashback.persistence.model;

import java.io.Serializable;
import java.time.LocalDate;

public class CashbackId implements Serializable {
    long userId;
    LocalDate rdate;

    public CashbackId(long userId, LocalDate rdate) {
        this.userId = userId;
        this.rdate = rdate;
    }

    public CashbackId() {
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public LocalDate getRdate() {
        return rdate;
    }

    public void setRdate(LocalDate rdate) {
        this.rdate = rdate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CashbackId that = (CashbackId) o;

        if (userId != that.userId) return false;
        return rdate.equals(that.rdate);
    }

    @Override
    public int hashCode() {
        int result = (int) (userId ^ (userId >>> 32));
        result = 31 * result + rdate.hashCode();
        return result;
    }
}
