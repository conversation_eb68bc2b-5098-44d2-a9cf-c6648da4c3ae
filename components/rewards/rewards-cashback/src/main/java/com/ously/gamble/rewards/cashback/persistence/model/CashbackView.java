package com.ously.gamble.rewards.cashback.persistence.model;

import com.ously.gamble.rewards.cashback.api.CashbackEvalInfo;
import com.ously.gamble.rewards.cashback.api.CashbackStatus;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.Immutable;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;

@EntityListeners(AuditingEntityListener.class)
@Entity
@Table(name = "rewards_cashback_view")

@Immutable
@IdClass(CashbackViewId.class)
public class CashbackView implements Persistable<CashbackViewId> {

    @Id
    @Column(name = "user_id")
    long userId;

    @Id
    @Column(name = "rdate")
    LocalDate rdate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    CashbackStatus status;

    @Column(name = "created_at", nullable = false)
    Instant createdAt;

    @Column(name = "ends_at", nullable = false)
    Instant endsAt;

    @Column(name = "deposit_id", nullable = false)
    String depositId;

    @Column(name = "bonus_id")
    Long bonusId;

    @Column(name = "last_eval_at")
    Instant lastEvalAt;

    @Column(name = "dep_factor")
    BigDecimal depFactor;

    @Column(name = "wager_factor")
    Integer wagerFactor;

    @Type(JsonType.class)
    @Column(name = "infos")
    CashbackEvalInfo info;

    @Override
    public CashbackViewId getId() {
        return new CashbackViewId(userId, rdate);
    }

    @Override
    public boolean isNew() {
        return false;
    }

    public BigDecimal getDepFactor() {
        return depFactor;
    }

    public Integer getWagerFactor() {
        return wagerFactor;
    }

    public long getUserId() {
        return userId;
    }


    public LocalDate getRdate() {
        return rdate;
    }


    public CashbackStatus getStatus() {
        return status;
    }


    public Instant getCreatedAt() {
        return createdAt;
    }


    public Instant getEndsAt() {
        return endsAt;
    }


    public String getDepositId() {
        return depositId;
    }


    public Long getBonusId() {
        return bonusId;
    }


    public CashbackEvalInfo getInfo() {
        return info;
    }


    public Instant getLastEvalAt() {
        return lastEvalAt;
    }
}
