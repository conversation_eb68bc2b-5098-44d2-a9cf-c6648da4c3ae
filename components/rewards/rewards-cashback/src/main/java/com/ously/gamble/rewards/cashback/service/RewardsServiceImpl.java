package com.ously.gamble.rewards.cashback.service;

import com.ously.gamble.api.rewards.CashbackDto;
import com.ously.gamble.api.rewards.RewardsService;
import com.ously.gamble.conditions.ConditionalOnOffloader;
import com.ously.gamble.rewards.cashback.api.CashbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * This will be moved to rewards core module
 */
@Service
@ConditionalOnOffloader
public class RewardsServiceImpl implements RewardsService {

    private final CashbackService cbSrv;

    public RewardsServiceImpl(@Autowired(required = false) CashbackService cbSrv) {
        this.cbSrv = cbSrv;
    }

    @Override
    public Optional<CashbackDto> getActiveCashback(long userId) {
        if (cbSrv == null) {
            return Optional.empty();
        }
        return cbSrv.evaluateCashback(userId);
    }
}
