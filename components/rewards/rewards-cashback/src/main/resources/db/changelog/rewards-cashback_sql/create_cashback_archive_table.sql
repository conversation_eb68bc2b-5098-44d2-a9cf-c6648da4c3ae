CREATE TABLE `rewards_cashback_archive`
(
    `user_id`      bigint                                  NOT NULL,
    `rdate`        date                                    NOT NULL,
    `created_at`   timestamp                               NOT NULL,
    `ends_at`      timestamp                               NOT NULL,
    `status`       varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL,
    `deposit_id`   varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `bonus_id`     bigint                                       DEFAULT NULL,
    `last_eval_at` timestamp                               NULL DEFAULT NULL,
    `infos`        json                                    NOT NULL,
    `dep_factor`   decimal(5, 2)                                DEFAULT NULL,
    `wager_factor` smallint                                     DEFAULT NULL,
    PRIMARY KEY (`user_id`, `rdate`),
    INDEX `rew-cb-arch-status` (`status`),
    INDEX `rew-cb-arch-idx` (`rdate`, `bonus_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;


create view `rewards_cashback_view` as
select *
from `rewards_cashback`
union all
select *
from `rewards_cashback_archive`;


create trigger rewards_cashback_on_delete
    after delete
    on rewards_cashback
    for each row
    replace into rewards_cashback_archive
    values (OLD.user_id, OLD.rdate, OLD.created_at, old.ends_at, old.status, old.deposit_id, old.bonus_id,
            old.last_eval_at, old.infos, old.dep_factor, old.wager_factor);
