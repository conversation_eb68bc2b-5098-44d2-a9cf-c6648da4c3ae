plugins {
    id 'java-library'
}

import org.gradle.internal.os.OperatingSystem

dependencies {
    // subs
    implementation project(':api')
    implementation project(':persistence')

    implementation "org.springframework.boot:spring-boot-starter-amqp:${springBootVersion}"
    implementation "org.springframework.data:spring-data-jpa"

    implementation "org.springframework.security:spring-security-core"

    implementation "org.springframework.amqp:spring-amqp"
    implementation "org.springframework.amqp:spring-rabbit"

//    implementation "org.springframework.pulsar:spring-pulsar"

//    implementation 'org.springframework.boot:spring-boot-starter-pulsar'

    implementation "org.springframework:spring-web"

    api("org.redisson:redisson-spring-data-31:${redissonStarterVersion}")
    api("org.redisson:redisson:${redissonStarterVersion}")

    if (OperatingSystem.current().isMacOsX()) {
        final String osArch = System.getProperty("os.arch")
        if(osArch.equals("aarch64")) {
            project.logger.warn("Adding netty-resolver for aarch")
            runtimeOnly "io.netty:netty-resolver-dns-native-macos::osx-aarch_64"
        }else{
            project.logger.warn("Adding netty-resolver for: x86")
            runtimeOnly "io.netty:netty-resolver-dns-native-macos::osx-x86_64"
        }
    }
    implementation("org.springframework.boot:spring-boot-actuator-autoconfigure:${springBootVersion}")

    implementation("org.springframework.boot:spring-boot-autoconfigure:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot:${springBootVersion}")


    implementation("org.springframework:spring-context")
    implementation("org.springframework:spring-core")
    implementation("org.springframework:spring-beans")

    api("net.javacrumbs.shedlock:shedlock-spring:${shedlockSpringVersion}")


    api("io.micrometer:micrometer-core")
    implementation "org.springframework.amqp:spring-amqp"
    api 'com.dynatrace.oneagent.sdk.java:oneagent-sdk:1.8.0'


    implementation("com.fasterxml.jackson.core:jackson-core")
    implementation("org.slf4j:slf4j-api") //:1.7.30
    implementation("org.springframework.data:spring-data-redis") //2.4.9
    implementation("jakarta.annotation:jakarta.annotation-api") //:1.3.5
    implementation("org.apache.httpcomponents:httpcore")

    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    implementation("com.fasterxml.jackson.module:jackson-module-blackbird")

    // Scheduler locking (so even if offloader scales, the scheduled tasks are only called once
    implementation "net.javacrumbs.shedlock:shedlock-provider-redis-spring:${shedlockSpringVersion}"
    implementation "com.fasterxml.jackson.module:jackson-module-parameter-names"
    implementation 'org.springframework:spring-webmvc'
}
