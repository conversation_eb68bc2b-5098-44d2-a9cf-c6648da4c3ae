package com.ously.gamble.safes;

import com.ously.gamble.api.features.AbstractPlatformFeature;
import com.ously.gamble.api.features.FeatureDescription;
import com.ously.gamble.api.features.PlatformFeature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConditionalOnProperty(prefix = "safes", name = "enabled", havingValue = "true")
public class SafeFeature extends AbstractPlatformFeature implements PlatformFeature {
    @Override
    public FeatureDescription getDescription() {
        return new FeatureDescription("Safe feature", "Handle timelocked safes - Social only");
    }

    public List<String> getDailyAnalyzeTables() {
        return List.of();
    }

    public List<String> getWeeklyAnalyzeTables() {
        return List.of();
    }
}
