package com.ously.gamble.safes.controller;

import com.ously.gamble.api.security.CurrentUser;
import com.ously.gamble.api.security.UserPrincipal;
import com.ously.gamble.conditions.ConditionalOnBackend;
import com.ously.gamble.safes.api.PlayerSafeInfo;
import com.ously.gamble.safes.api.SafeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@ConditionalOnBackend
public class SafeController {

    private final SafeService safeService;

    public SafeController(@Autowired(required = false) SafeService sfSrv) {
        this.safeService = sfSrv;
    }


    //
    // get users safe infos
    //
    @Operation(description = "get player safe info",
            security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping(value = "/api/app/sf")
    @RolesAllowed("USER")
    public ResponseEntity<PlayerSafeInfo> getPlayerSafeInfo(@CurrentUser UserPrincipal currentUser) {
        if (safeService == null) {
            return ResponseEntity.of(Optional.empty());
        }
        return ResponseEntity.ok(safeService.getPlayersSafeInfo(currentUser.getId()));
    }

}
