package com.ously.gamble.safes.persistence.model;

import java.io.Serializable;

public class ActiveSafeId implements Serializable {


    long userId;
    String safeId;

    public ActiveSafeId() {
    }

    public ActiveSafeId(long userId, String safeId) {
        this.userId = userId;
        this.safeId = safeId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getSafeId() {
        return safeId;
    }

    public void setSafeId(String safeId) {
        this.safeId = safeId;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;

        ActiveSafeId that = (ActiveSafeId) o;
        return userId == that.userId && safeId.equals(that.safeId);
    }

    @Override
    public int hashCode() {
        int result = Long.hashCode(userId);
        result = 31 * result + safeId.hashCode();
        return result;
    }
}
