package com.ously.gamble.safes.persistence.model;


import com.ously.gamble.safes.api.SafeType;
import jakarta.persistence.*;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;

@Entity
@Table(name = "safe_backlog")
@IdClass(BacklogSafeId.class)
@EntityListeners(AuditingEntityListener.class)
public class BacklogSafe implements Persistable<BacklogSafeId> {

    @Id
    @Column(name = "user_id")
    long userId;

    @Id
    @Column(name = "safe_id")
    String safeId;

    @Column(name = "safe_type")
    @Enumerated(EnumType.STRING)
    SafeType type;

    @Column(name = "created_at")
    Instant createdAt;

    @Transient
    boolean wasLoaded;

    @PostLoad
    @PostPersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }

    @Override
    public boolean isNew() {
        return !wasLoaded;
    }

    @Override
    public BacklogSafeId getId() {
        return new BacklogSafeId(userId, safeId);
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public boolean isWasLoaded() {
        return wasLoaded;
    }

    public void setWasLoaded(boolean wasLoaded) {
        this.wasLoaded = wasLoaded;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getSafeId() {
        return safeId;
    }

    public void setSafeId(String safeId) {
        this.safeId = safeId;
    }

    public SafeType getType() {
        return type;
    }

    public void setType(SafeType type) {
        this.type = type;
    }
}
