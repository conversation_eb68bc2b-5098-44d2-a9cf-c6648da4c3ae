package com.ously.gamble.safes.persistence.repositories;

import com.ously.gamble.safes.persistence.model.ActiveSafe;
import com.ously.gamble.safes.persistence.model.ActiveSafeId;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
@ConditionalOnProperty(prefix = "safes", name = "enabled", havingValue = "true")
public interface ActiveSafeRepository extends JpaRepository<ActiveSafe, ActiveSafeId> {

    List<ActiveSafe> findAllByUserId(long userId);

    @Query(nativeQuery = true, value = "select level+1 from wallets where user_id = :uid")
    int getPlayerLevel(@Param("uid") long userId);

    @Query
    int countByUserId(long userId);
}
