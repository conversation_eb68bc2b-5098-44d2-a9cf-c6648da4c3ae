package com.ously.gamble.safes.util;

import com.ously.gamble.safes.api.SafeType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Helper for the first implementation. We will externalize the configuration of safes into a json structure which then gets read from the DB
 */
public class SafeConfigurationHelper {

    private static final Map<SafeType, SafeTypeConfig> safeTypeConfigs;

    static {
        safeTypeConfigs = new ConcurrentHashMap<>(6);
        safeTypeConfigs.put(SafeType.WOOD, createWoodConfig());
        safeTypeConfigs.put(SafeType.SILVER, createSilverConfig());
        safeTypeConfigs.put(SafeType.GOLD, createGoldConfig());
        safeTypeConfigs.put(SafeType.EPIC, createEpicConfig());
        safeTypeConfigs.put(SafeType.LEGENDARY, createLegendaryConfig());
        safeTypeConfigs.put(SafeType.ULTIMATE, createUltimateConfig());
    }

    public static SafeTypeConfig getSafeTypeConfig(SafeType safeType) {
        return safeTypeConfigs.get(safeType);
    }

    private static SafeTypeConfig createWoodConfig() {
        List<SafeLevelConfig> safeLevelConfigs = new ArrayList<>();
        safeLevelConfigs.add(new SafeLevelConfig(1, 15, 1000, 2000, 1, 1));
        safeLevelConfigs.add(new SafeLevelConfig(16, 30, 3000, 5000, 2, 1));
        safeLevelConfigs.add(new SafeLevelConfig(31, 50, 5000, 10000, 3, 1));
        safeLevelConfigs.add(new SafeLevelConfig(51, 100, 6000, 12000, 4, 1));
        safeLevelConfigs.add(new SafeLevelConfig(101, 200, 7500, 15000, 5, 1));
        safeLevelConfigs.add(new SafeLevelConfig(201, 400, 10000, 17500, 6, 1));
        safeLevelConfigs.add(new SafeLevelConfig(401, 99999, 20000, 30000, 8, 1));
        return new SafeTypeConfig(10, "PT15M", safeLevelConfigs);
    }

    private static SafeTypeConfig createSilverConfig() {
        List<SafeLevelConfig> safeLevelConfigs = new ArrayList<>();
        safeLevelConfigs.add(new SafeLevelConfig(1, 15, 2000, 4000, 2, 2));
        safeLevelConfigs.add(new SafeLevelConfig(16, 30, 7500, 10000, 3, 2));
        safeLevelConfigs.add(new SafeLevelConfig(31, 50, 15000, 20000, 4, 2));
        safeLevelConfigs.add(new SafeLevelConfig(51, 100, 20000, 30000, 5, 2));
        safeLevelConfigs.add(new SafeLevelConfig(101, 200, 25000, 35000, 6, 2));
        safeLevelConfigs.add(new SafeLevelConfig(201, 400, 30000, 40000, 8, 2));
        safeLevelConfigs.add(new SafeLevelConfig(401, 99999, 40000, 50000, 10, 2));
        return new SafeTypeConfig(20, "PT30M", safeLevelConfigs);
    }

    private static SafeTypeConfig createGoldConfig() {
        List<SafeLevelConfig> safeLevelConfigs = new ArrayList<>();
        safeLevelConfigs.add(new SafeLevelConfig(1, 15, 4000, 8000, 3, 3));
        safeLevelConfigs.add(new SafeLevelConfig(16, 30, 15000, 25000, 4, 3));
        safeLevelConfigs.add(new SafeLevelConfig(31, 50, 30000, 40000, 5, 3));
        safeLevelConfigs.add(new SafeLevelConfig(51, 100, 40000, 60000, 6, 3));
        safeLevelConfigs.add(new SafeLevelConfig(101, 200, 50000, 75000, 8, 3));
        safeLevelConfigs.add(new SafeLevelConfig(201, 400, 60000, 80000, 10, 3));
        safeLevelConfigs.add(new SafeLevelConfig(401, 99999, 75000, 100000, 12, 3));
        return new SafeTypeConfig(45, "PT90M", safeLevelConfigs);
    }

    private static SafeTypeConfig createEpicConfig() {
        List<SafeLevelConfig> safeLevelConfigs = new ArrayList<>();
        safeLevelConfigs.add(new SafeLevelConfig(1, 15, 10000, 20000, 4, 2));
        safeLevelConfigs.add(new SafeLevelConfig(16, 30, 30000, 50000, 5, 3));
        safeLevelConfigs.add(new SafeLevelConfig(31, 50, 50000, 75000, 6, 4));
        safeLevelConfigs.add(new SafeLevelConfig(51, 100, 75000, 100000, 7, 4));
        safeLevelConfigs.add(new SafeLevelConfig(101, 200, 80000, 120000, 10, 4));
        safeLevelConfigs.add(new SafeLevelConfig(201, 400, 100000, 150000, 12, 4));
        safeLevelConfigs.add(new SafeLevelConfig(401, 99999, 125000, 175000, 15, 4));
        return new SafeTypeConfig(80, "PT3H", safeLevelConfigs);
    }


    private static SafeTypeConfig createLegendaryConfig() {
        List<SafeLevelConfig> safeLevelConfigs = new ArrayList<>();
        safeLevelConfigs.add(new SafeLevelConfig(1, 15, 40000, 60000, 5, 3));
        safeLevelConfigs.add(new SafeLevelConfig(16, 30, 75000, 125000, 6, 3));
        safeLevelConfigs.add(new SafeLevelConfig(31, 50, 100000, 200000, 7, 3));
        safeLevelConfigs.add(new SafeLevelConfig(51, 100, 150000, 250000, 10, 4));
        safeLevelConfigs.add(new SafeLevelConfig(101, 200, 175000, 300000, 12, 4));
        safeLevelConfigs.add(new SafeLevelConfig(201, 400, 200000, 400000, 15, 4));
        safeLevelConfigs.add(new SafeLevelConfig(401, 99999, 250000, 500000, 20, 4));
        return new SafeTypeConfig(125, "PT6H", safeLevelConfigs);
    }


    private static SafeTypeConfig createUltimateConfig() {
        List<SafeLevelConfig> safeLevelConfigs = new ArrayList<>();
        safeLevelConfigs.add(new SafeLevelConfig(1, 15, 75000, 125000, 6, 3));
        safeLevelConfigs.add(new SafeLevelConfig(16, 30, 200000, 300000, 8, 3));
        safeLevelConfigs.add(new SafeLevelConfig(31, 50, 250000, 400000, 10, 4));
        safeLevelConfigs.add(new SafeLevelConfig(51, 100, 300000, 450000, 12, 4));
        safeLevelConfigs.add(new SafeLevelConfig(101, 200, 450000, 600000, 15, 4));
        safeLevelConfigs.add(new SafeLevelConfig(201, 400, 500000, 750000, 20, 5));
        safeLevelConfigs.add(new SafeLevelConfig(401, 99999, 750000, 1500000, 30, 5));
        return new SafeTypeConfig(200, "PT12H", safeLevelConfigs);
    }

    /**
     * Returns a random safe type based on predefined probability distribution
     *
     * @return A randomly selected SafeType
     */
    public static SafeType getTypeForRandomSafe() {
        // Use ThreadLocalRandom for better performance and thread safety
        double randomValue = ThreadLocalRandom.current().nextDouble(100.0);

        // Define probability thresholds for each safe type
        final double WOOD_THRESHOLD = 25.0;      // 25% chance
        final double SILVER_THRESHOLD = 50.0;    // 25% chance
        final double GOLD_THRESHOLD = 70.0;      // 20% chance
        final double EPIC_THRESHOLD = 90.0;      // 20% chance
        final double LEGENDARY_THRESHOLD = 96.0; // 6% chance
                                                // Ultimate: 4% chance

        if (randomValue < WOOD_THRESHOLD) {
            return SafeType.WOOD;
        } else if (randomValue < SILVER_THRESHOLD) {
            return SafeType.SILVER;
        } else if (randomValue < GOLD_THRESHOLD) {
            return SafeType.GOLD;
        } else if (randomValue < EPIC_THRESHOLD) {
            return SafeType.EPIC;
        } else if (randomValue < LEGENDARY_THRESHOLD) {
            return SafeType.LEGENDARY;
        }
        return SafeType.ULTIMATE;
    }

}
