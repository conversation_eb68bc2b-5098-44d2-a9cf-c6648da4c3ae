plugins {
    id 'java-library'
}


dependencies {

    implementation project(':persistence')
    implementation project(':api')
    implementation project(':services')


    api project(':features:rtp')
    api project(':features:ranking-api')

    implementation project(':features:bonuscodes')
    implementation project(':components:autotagger')
    implementation project(':components:issuemanager')
    implementation project(':components:notification-sms')
    implementation project(':components:notification-email')
    implementation project(':components:gamemanager')
    implementation project(':payments:manager-api')
    implementation project(':components:geoip-db')
    implementation project(':components:useraudit')
    implementation project(':components:videoads')
    implementation project(':features:marketing-emails')

    implementation project(':components:localisation')


    api project(':devtools:querydsl-expressions')


    implementation 'org.springframework:spring-web'

    api "io.swagger.core.v3:swagger-annotations:${swaggerCoreVersion}"
    implementation "org.springframework.security:spring-security-core"


    api "org.springframework.amqp:spring-rabbit"

    // Helper for statistics query table refinement
    implementation 'com.github.martincooper:java-datatable:1.0.0'

    implementation "net.javacrumbs.shedlock:shedlock-spring:${shedlockSpringVersion}"

    implementation 'com.google.guava:guava:33.3.0-jre'
    implementation "org.springframework.boot:spring-boot-starter-mail"
    implementation "org.springframework.boot:spring-boot-starter-freemarker"

    implementation 'org.springframework:spring-webmvc'
    implementation 'org.springframework.security:spring-security-web'
    implementation 'org.springframework.security:spring-security-config'

    api 'org.apache.commons:commons-collections4:4.4'
    implementation "commons-codec:commons-codec:1.15"

    implementation("com.github.luben:zstd-jni:1.5.2-2")
}

