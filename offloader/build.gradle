plugins {
    id 'java'
    id 'org.springframework.boot' version "${springBootVersion}"
    id "com.gorylenko.gradle-git-properties" version "${gitPropertyPluginVersion}"
    id 'com.google.cloud.tools.jib'

}

apply plugin: 'io.spring.dependency-management'

configurations {
    compile.exclude module: "spring-boot-starter-tomcat"
}

tasks.withType(Copy).configureEach {
    duplicatesStrategy 'warn'
}

springBoot {
    buildInfo()
}

bootRun {
    jvmArgs += ["--add-opens=java.base/java.time=ALL-UNNAMED", "--add-opens=java.base/sun.net=ALL-UNNAMED","--add-opens=java.base/java.lang=ALL-UNNAMED", "--add-opens=java.base/java.math=ALL-UNNAMED", "--add-opens=java.base/java.util=ALL-UNNAMED", "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED", "--add-opens=java.base/java.net=ALL-UNNAMED", "--add-opens=java.base/java.text=ALL-UNNAMED", "--add-opens=java.sql/java.sql=ALL-UNNAMED"]
}

test {
    useJUnitPlatform()
    systemProperty 'junit.jupiter.extensions.autodetection.enabled', 'true'
    systemProperty 'flyway.enabled', 'false'
}

dependencies {
    runtimeOnly "org.springframework.boot:spring-boot-properties-migrator"
    implementation 'org.springframework.boot:spring-boot-starter-log4j2'

    // subs
    implementation project(':persistence')
    implementation project(':api')
    implementation project(':services')
    implementation project(':offloader-shared')

    testImplementation project(':test-util')

    testImplementation("com.google.firebase:firebase-admin:${firebaseVersion}")

    // components
    implementation project(':components:notification-push')
    implementation project(':components:authjwt-firebase')
    implementation project(':components:storage-s3')
    implementation project(':components:crm-customerio')
    implementation project(':components:gamemanager')
    implementation project(':components:videoads')
    implementation project(':components:firestore')
    implementation project(':components:reporting')

    implementation project(':components:appsflyer')


    implementation project(':components:rewards:rewards-tickets')

    implementation project(':features:social-common')

    implementation project(':features:ranking')
    implementation project(':features:jackpots')
    implementation project(':features:safes')
    implementation project(':features:leaderboards')
    implementation project(':features:doubleup')
    implementation project(':features:loyalty')
    implementation project(':features:missions')
    implementation project(':features:wheelspins')
    implementation project(':features:bonuscodes')
    implementation project(':payments:payment-mobile')

    implementation project(':features:milestones')

    implementation project(':features:cpopups')

    implementation project(':features:marketing-emails')

    implementation "net.javacrumbs.shedlock:shedlock-spring:${shedlockSpringVersion}"

    // aop
    implementation "org.springframework.boot:spring-boot-starter-aop"

    // Swagger stuff
    if (project.hasProperty("swagger")) {
        implementation project(':devtools:swagger-ui')
    }


    // monitoring
    implementation "org.springframework.boot:spring-boot-starter-actuator"
    implementation "io.micrometer:micrometer-registry-prometheus"

    // using jetty!
    implementation "org.springframework.boot:spring-boot-starter-web", {
        exclude group: "org.springframework.boot", module: "spring-boot-starter-tomcat"
    }
    implementation "org.springframework.boot:spring-boot-starter-jetty", {
        exclude group: "org.eclipse.jetty.ee10.websocket"
    }

    implementation "org.springframework.boot:spring-boot-starter-security"

    implementation "org.springframework.boot:spring-boot-starter-data-jpa"


    //
    implementation("com.github.gavlyukovskiy:datasource-proxy-spring-boot-starter:${datasourceProxyVersion}")

}

compileJava.dependsOn.add('generateGitProperties')
compileJava.dependsOn.add('bootBuildInfo')
compileJava.inputs.files(processResources)

ext {
    // ...
    ext.getGitHash = { ->
        def stdout = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'rev-parse', '--short=8', 'HEAD'
            standardOutput = stdout
        }
        return stdout.toString().trim()
    }
}


jib {
    from {
        image = '666067033667.dkr.ecr.eu-central-1.amazonaws.com/sa-base:ac21.0.1.al2net'
        platforms {
            platform {
                architecture = 'amd64'
                os = 'linux'
            }
        }
        if (System.env['AWS_ECR_PW'] != null) {
            auth {
                username = 'AWS'
                password = "$System.env.AWS_ECR_PW"
            }
        }
    }
    to {
        image = '666067033667.dkr.ecr.eu-central-1.amazonaws.com/sa-offloader'
        if (System.env['AWS_ECR_PW'] != null) {
            tags = ['latest', "${getGitHash()}"]
        } else {
            tags = ['latest', "LOCAL-${getGitHash()}"]
        }
        if (System.env['AWS_ECR_PW'] != null) {
            auth {
                username = 'AWS'
                password = "$System.env.AWS_ECR_PW"
            }
        }
    }
    container {
        appRoot = '/offloader'
        jvmFlags = ["@/offloader/resources/java-21-args", "-Xms1024M", "-Xmx1124M", "-XX:+UseZGC", "-XX:+ZGenerational", "-XX:+UseCompressedOops", "-XX:+UseStringDeduplication"]
        ports = ['8080']
        workingDirectory = '/offloader'
    }
    allowInsecureRegistries = false
}
if (System.env['AWS_ECR_PW'] == null) {
    tasks.jib.dependsOn rootProject.loginToECR
}
