package com.ously.gamble.payment.coinspaid.api.restapi;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(Include.NON_NULL)
public record CPPGetDepositAddressRequest(
        @JsonProperty("foreign_id") String foreignId,
        @JsonProperty("currency")String currency,
        @JsonProperty("convert_to")String convertToCurrency
) {

    @Override
    public String toString() {
        return "CPPGetDepositAddressRequest{" +
               "foreignId='" + foreignId + '\'' +
               ", currency='" + currency + '\'' +
               ", convertToCurrency='" + convertToCurrency + '\'' +
               '}';
    }
}
