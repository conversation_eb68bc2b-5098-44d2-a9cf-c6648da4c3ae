package com.ously.gamble.payment.coinspaid.api.restapi;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(Include.NON_NULL)
public record CPPSendWithdrawRequest(
        @JsonProperty("foreign_id")
        String foreignId,
        String amount,
        String currency,
        @JsonProperty("convert_to")
        String convertTo,
        String address,
        String tag
        ) {

        @Override
        public String toString() {
                return "CPPSendWithdrawRequest{" +
                       "foreignId='" + foreignId + '\'' +
                       ", amount='" + amount + '\'' +
                       ", currency='" + currency + '\'' +
                       ", convertTo='" + convertTo + '\'' +
                       ", address='" + address + '\'' +
                       ", tag='" + tag + '\'' +
                       '}';
        }
}
