package com.ously.gamble.payment.coinspaid.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
@ConfigurationProperties(prefix = "payment.coinspaid")
@ConditionalOnProperty(prefix = "payment.coinspaid", name = "enabled", havingValue = "true")
public class CoinspaidConfig {

    /**
     * disable when not needed
     */
    boolean enabled;

    boolean listener;

    boolean ignoreWrongSignature = true;

    BigDecimal maxdepositinusd = BigDecimal.valueOf(1_000_000L);

    public static final String HANDLERNAME = "xcp";

    /**
     * The secret used to create the signature of the payload (send and received as header 'X-Processing-Signature')
     */
    String privateKey = "d68CM4wjEeVOoiMkScNGcxXGZ6qoWmhqDvGdzrGVcznO18AhpoVz3kZH6hwcQC0L";
    /**
     * The public account identifier (send as header 'X-Processing-Key')
     */
    String publicKey = "5q33SuSXh0HdTqMuEBotiL8JbqmPwQLF";
    /**
     * The api baseurl (up to the version identifier <a href="https://...../api/v2">...</a>
     */
    String apiurl = "https://app.sandbox.cryptoprocessing.com/api/v2";


    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getApiurl() {
        return apiurl;
    }

    public void setApiurl(String apiurl) {
        this.apiurl = apiurl;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isListener() {
        return listener;
    }

    public void setListener(boolean listener) {
        this.listener = listener;
    }

    public boolean isIgnoreWrongSignature() {
        return ignoreWrongSignature;
    }

    public void setIgnoreWrongSignature(boolean ignoreWrongSignature) {
        this.ignoreWrongSignature = ignoreWrongSignature;
    }

    public BigDecimal getMaxdepositinusd() {
        return maxdepositinusd;
    }

    public void setMaxdepositinusd(BigDecimal maxdepositinusd) {
        this.maxdepositinusd = maxdepositinusd;
    }

}
