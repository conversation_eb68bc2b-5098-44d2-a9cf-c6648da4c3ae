package com.ously.gamble.payment.coinspaid.service.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ously.gamble.payment.api.actions.ActionType;
import com.ously.gamble.payment.api.actions.ManagerAction;
import com.ously.gamble.payment.api.actions.handler.HandleNotificationAction;
import com.ously.gamble.payment.api.actions.manager.AddOrUpdateDepositAction;
import com.ously.gamble.payment.api.actions.manager.UpdateWithdrawAction;
import com.ously.gamble.payment.api.handler.info.ActionStatus;
import com.ously.gamble.payment.api.handler.info.PaymentContext;
import com.ously.gamble.payment.coinspaid.api.ActionHandler;
import com.ously.gamble.payment.coinspaid.api.notification.CPNotification;
import com.ously.gamble.payment.coinspaid.config.CoinspaidConfig;
import com.ously.gamble.payment.coinspaid.util.CoinspaidHandlerUtils;
import org.apache.commons.lang3.NotImplementedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;

@Component
@ConditionalOnBean(CoinspaidConfig.class)
public class NotificationHandler implements ActionHandler<HandleNotificationAction> {

    private final Logger log = LoggerFactory.getLogger(NotificationHandler.class);

    private final ObjectMapper om;


    public NotificationHandler(ObjectMapper om) {
        this.om = om;
    }

    @Override
    public ActionType getHandledActionType() {
        return ActionType.CALLBACK_HANDLE;
    }

    @Override
    public Collection<? extends ManagerAction> performAction(HandleNotificationAction action,
                                                             PaymentContext ctx) {
        log.info("Handle notificationAction:{}", action);
        try {
            var notification = om.readValue(action.getNotification().getBody(),
                    CPNotification.class);
            switch (notification.type()) {
                case deposit, deposit_exchange -> {
                    var dpCtx = CoinspaidHandlerUtils.createContextFromDepositNotification(notification);
                    var aouda = new AddOrUpdateDepositAction(dpCtx,
                            CoinspaidHandlerUtils.createDepositUpdate(notification,
                                    action.getNotification().getMd5(),
                                    om));
                    ctx.addLogEntry(action, ActionStatus.OK, "Created DepositUpdateAction with {} " +
                                                             "and {}", aouda.getUpdate(), aouda.getContext());
                    return Collections.singletonList(aouda);
                }
                case withdrawal, withdrawal_exchange -> {
                    var dpCtx =
                            CoinspaidHandlerUtils.createContextFromWithdrawNotification(notification);
                    var aouda = new UpdateWithdrawAction(dpCtx,
                            CoinspaidHandlerUtils.createWithdrawUpdate(notification,
                                    action.getNotification().getMd5(),
                                    om));
                    ctx.addLogEntry(action, ActionStatus.OK, "Created WithdrawUpdateAction with {} " +
                                                             "and {}", aouda.getUpdate(), aouda.getContext());
                    return Collections.singletonList(aouda);
                }
                case exchange -> {
                    ctx.addLogEntry(action, ActionStatus.OK, "Ignoring Exchange notification");
                    return Collections.emptyList();
                }
                default -> throw new NotImplementedException("NotificationType " + notification.type() + " not " +
                                                             "implemented " +
                                                             "yet");
            }
        } catch (Exception e) {
            ctx.addLogEntry(action, ActionStatus.ERROR, Arrays.toString(e.getStackTrace()));
            log.error("Error handling Coinspaid.handleNotification", e.getCause());
        }
        return Collections.emptyList();
    }
}
