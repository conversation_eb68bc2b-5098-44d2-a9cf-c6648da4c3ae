package com.ously.gamble.payment.manager.persistence.idclasses;

import java.io.Serializable;
import java.util.Objects;

public class PaymentPayoutId implements Serializable {
    Long userId;
    String payoutId;

    public PaymentPayoutId() {
    }

    public PaymentPayoutId(Long userId, String paymentId) {
        this.userId = userId;
        this.payoutId = paymentId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getPayoutId() {
        return payoutId;
    }

    public void setPayoutId(String payoutId) {
        this.payoutId = payoutId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        var paymentId1 = (PaymentPayoutId) o;

        return Objects.equals(userId, paymentId1.userId) && Objects.equals(payoutId, paymentId1.payoutId);
    }

    @Override
    public int hashCode() {
        var result = userId != null ? userId.hashCode() : 0;
        result = 31 * result + (payoutId != null ? payoutId.hashCode() : 0);
        return result;
    }
}
