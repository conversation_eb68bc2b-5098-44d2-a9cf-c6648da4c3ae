package com.ously.gamble.payment.manager.persistence.model;

import com.ously.gamble.payment.api.PaymentStatus;
import com.ously.gamble.payment.api.UserPaymentStatistic;
import com.ously.gamble.payment.manager.persistence.idclasses.PaymentDepositId;
import jakarta.persistence.*;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;

@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "payment_deposit")
@IdClass(PaymentDepositId.class)


@NamedNativeQuery(name = "PaymentDeposit.getPaymentStatisticForUser",
        query = """
                select numDeposits,coalesce(sumDeposits,0) as sumDeposits,numPayouts,coalesce(sumPayouts,0) as sumPayouts from
                                                                (select ?1 as userId, count(*) as numDeposits, sum(amount) as sumDeposits from payment_deposit where user_id = ?1 and status='SUCCESS')dp join (
                                                                select ?1 as userId,count(*) as numPayouts, sum(amount) as sumPayouts from payment_payout where user_id = ?1 and status='SUCCESS') po on dp.userId=po.userId
                                                                """,
        resultSetMapping = "Mapping.PaymentStatistics")

@SqlResultSetMapping(
        name = "Mapping.PaymentStatistics", classes = @ConstructorResult(
        targetClass = UserPaymentStatistic.class,
        columns = {
                @ColumnResult(name = "numDeposits", type = int.class),
                @ColumnResult(name = "sumDeposits", type = BigDecimal.class),
                @ColumnResult(name = "numPayouts", type = int.class),
                @ColumnResult(name = "sumPayouts", type = BigDecimal.class)
        }
)
)

public class PaymentDeposit implements Persistable<PaymentDepositId> {

    @Transient
    boolean wasLoaded;

    @PostLoad
    @PrePersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }

    @Id
    @Column(name = "user_id")
    long userId;

    @Id
    @Column(name = "deposit_id", length = 200)
    String depositId;

    @Version
    @Column(name = "version")
    int version = 1;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    PaymentStatus status;

    @Column(name = "handler")
    String handler;

    @Column(name = "amount")
    BigDecimal amount = BigDecimal.ZERO;

    @Column(name = "fee")
    BigDecimal fee = BigDecimal.ZERO;

    @Column(name = "conv_rate")
    BigDecimal conversionRate = BigDecimal.ZERO;

    @Column(name = "currency")
    String currency;

    @Column(name = "currency_to")
    String currencyTo;

    @Column(name = "book_id")
    Long internalTransactionId;

    @Column(name = "created_at")
    Instant createdAt;

    @Column(name = "updated_at")
    Instant updatedAt;


    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Long getInternalTransactionId() {
        return internalTransactionId;
    }

    public void setInternalTransactionId(Long internalTransactionId) {
        this.internalTransactionId = internalTransactionId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getDepositId() {
        return depositId;
    }

    public void setDepositId(String depositId) {
        this.depositId = depositId;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public PaymentStatus getStatus() {
        return status;
    }

    public void setStatus(PaymentStatus status) {
        this.status = status;
    }

    public String getHandler() {
        return handler;
    }

    public void setHandler(String handler) {
        this.handler = handler;
    }

    @Override
    public PaymentDepositId getId() {
        return new PaymentDepositId(userId, depositId);
    }

    @Override
    public boolean isNew() {
        return !wasLoaded;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public String getCurrencyTo() {
        return currencyTo;
    }

    public void setCurrencyTo(String currencyTo) {
        this.currencyTo = currencyTo;
    }
}
