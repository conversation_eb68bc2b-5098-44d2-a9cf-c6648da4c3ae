package com.ously.gamble.payment.manager.persistence.model;

import com.ously.gamble.payment.api.PaymentStatus;
import com.ously.gamble.payment.api.handler.info.PaymentContext;
import com.ously.gamble.payment.manager.persistence.idclasses.PaymentPayoutStepId;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.Immutable;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;

@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "payment_payout_step")
@IdClass(PaymentPayoutStepId.class)
@Immutable
public class PaymentPayoutStep implements Persistable<PaymentPayoutStepId> {

    @Transient
    boolean wasLoaded;

    @PostLoad
    @PrePersist
    public void setTransientLoaded() {
        this.wasLoaded = true;
    }

    @Id
    @Column(name = "user_id")
    Long userId;

    @Id
    @Column(name = "payout_id")
    String payoutId;

    @Id
    @Column(name = "seq")
    Integer seq = 0;

    @Column(name = "status_before")
    @Enumerated(EnumType.STRING)
    PaymentStatus statusBefore;

    @Column(name = "status_after")
    @Enumerated(EnumType.STRING)
    PaymentStatus statusAfter;

    @Column(name = "notification_md5")
    String notificationId;

    @Column(name = "created_at")
    Instant createdAt;

    @Type(JsonType.class)
    @Column(name = "info")
    PaymentContext info;

    @Override
    public PaymentPayoutStepId getId() {
        return new PaymentPayoutStepId(userId, payoutId, seq);
    }

    @Override
    public boolean isNew() {
        return !wasLoaded;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getPayoutId() {
        return payoutId;
    }

    public void setPayoutId(String payoutId) {
        this.payoutId = payoutId;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public PaymentStatus getStatusBefore() {
        return statusBefore;
    }

    public void setStatusBefore(
            PaymentStatus statusBefore) {
        this.statusBefore = statusBefore;
    }

    public PaymentStatus getStatusAfter() {
        return statusAfter;
    }

    public void setStatusAfter(PaymentStatus statusAfter) {
        this.statusAfter = statusAfter;
    }

    public String getNotificationId() {
        return notificationId;
    }

    public void setNotificationId(String notificationId) {
        this.notificationId = notificationId;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public PaymentContext getInfo() {
        return info;
    }

    public void setInfo(PaymentContext info) {
        this.info = info;
    }
}
