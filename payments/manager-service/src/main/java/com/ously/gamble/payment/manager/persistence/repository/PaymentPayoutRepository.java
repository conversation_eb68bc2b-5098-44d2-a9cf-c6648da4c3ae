package com.ously.gamble.payment.manager.persistence.repository;

import com.ously.gamble.payment.api.PaymentStatus;
import com.ously.gamble.payment.manager.persistence.idclasses.PaymentPayoutId;
import com.ously.gamble.payment.manager.persistence.model.PaymentPayout;
import com.ously.gamble.payment.manager.persistence.model.QPaymentPayout;
import com.ously.gamble.payment.payload.payout.PayoutPeriodSums;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.galegofer.spring.data.querydsl.value.operators.ExpressionProviderFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Collection;

@Repository
@ConditionalOnProperty(prefix = "payment.manager", name = "enabled", havingValue = "true")
public interface PaymentPayoutRepository extends JpaRepository<PaymentPayout, PaymentPayoutId>
        , QuerydslPredicateExecutor<PaymentPayout>, QuerydslBinderCustomizer<QPaymentPayout> {

    @Query(value = "select p from PaymentPayout p where p.userId = :userId and p.createdAt > " +
                   ":from and p.createdAt < :to")
    Page<PaymentPayout> findAllByUserId(@Param("userId") Long userId,
                                        @Param("from") Instant from,
                                        @Param("to") Instant to, Pageable pageabe);


    @SuppressWarnings("NullableProblems")
    @Override
    default void customize(QuerydslBindings bindings, QPaymentPayout root) {
        bindings.bind(String.class).first(
                (SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);

        bindings.bind(String.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Long.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Integer.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Double.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Boolean.class).all(ExpressionProviderFactory::getPredicate);
        bindings.bind(Instant.class).all(ExpressionProviderFactory::getPredicate);

    }

    @Query(nativeQuery = true)
    PayoutPeriodSums getPayoutSums(long userId);

    @Query(value = "select p from PaymentPayout p where p.createdAt > " +
                   ":from and p.createdAt < :to and p.status in (:statusList)")
    Page<PaymentPayout> findAllByStatusIn(@Param("statusList") Collection<PaymentStatus> statusList,
                                          @Param("from") Instant from,
                                          @Param("to") Instant to, Pageable pageabe);
}
