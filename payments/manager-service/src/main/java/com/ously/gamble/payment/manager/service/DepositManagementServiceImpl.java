package com.ously.gamble.payment.manager.service;

import com.ously.gamble.payment.api.DepositManagementService;
import com.ously.gamble.payment.api.PaymentStatus;
import com.ously.gamble.payment.manager.config.PaymentManagerConfig;
import com.ously.gamble.payment.manager.persistence.model.PaymentDeposit;
import com.ously.gamble.payment.manager.persistence.repository.PaymentDepositRepository;
import com.ously.gamble.payment.manager.persistence.repository.PaymentDepositStepRepository;
import com.ously.gamble.payment.payload.PMCurrency;
import com.ously.gamble.payment.payload.deposit.UserDeposit;
import com.ously.gamble.payment.payload.deposit.UserDepositStep;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;

@ConditionalOnBean(PaymentManagerConfig.class)
@Service
public class DepositManagementServiceImpl implements DepositManagementService {

    private final PaymentDepositRepository pdpRepo;
    private final PaymentDepositStepRepository pdStepRepo;

    public DepositManagementServiceImpl(PaymentDepositRepository pdRepo,
                                        PaymentDepositStepRepository pdStepRepo) {
        this.pdpRepo = pdRepo;
        this.pdStepRepo = pdStepRepo;
    }

    @Override
    @Transactional
    public Page<UserDeposit> getDepositsForUser(Long userId, LocalDate from,
                                                LocalDate to, Pageable pageable) {
        return convertToPagedDeposits(pdpRepo.findAllByUserId(userId, toInstant(from),
                toInstant(to),
                pageable));
    }

    private Instant toInstant(LocalDate date) {
        return date.atStartOfDay().toInstant(ZoneOffset.UTC);
    }

    @Override
    @Transactional(readOnly = true)
    public UserDeposit getDeposit(String depositId) {
        var a = pdpRepo.findByDepositId(depositId);
        return new UserDeposit(a.getDepositId(),
                a.getUserId(), a.getStatus().name(),
                PMCurrency.valueOf(a.getCurrency()),
                PMCurrency.USD,
                a.getAmount(),
                a.getFee(),
                a.getConversionRate(),
                a.getCreatedAt(),
                (a.getStatus() == PaymentStatus.SUCCESS) ? a.getUpdatedAt() : null);
    }

    private static PageImpl<UserDeposit> convertToPagedDeposits(Page<PaymentDeposit> page) {
        var lg = page.getContent().stream().map(a -> new UserDeposit(a.getDepositId(),
                a.getUserId(), a.getStatus().name(),
                PMCurrency.valueOf(a.getCurrency()),
                PMCurrency.USD,
                a.getAmount(),
                a.getFee(),
                a.getConversionRate(),
                a.getCreatedAt(),
                (a.getStatus() == PaymentStatus.SUCCESS) ? a.getUpdatedAt() : null)).toList();
        return new PageImpl<>(lg, page.getPageable(), page.getTotalElements());
    }

    @Override
    public Page<UserDeposit> getAllDeposits(LocalDate from, LocalDate to, Pageable pageable) {
        return convertToPagedDeposits(pdpRepo.findAll(toInstant(from), toInstant(to), pageable));
    }

    @Override
    @Transactional
    public List<UserDepositStep> getStepsForDeposit(Long userId, String depositId) {
        return pdStepRepo.findAllByUserIdAndDepositIdOrderBySeq(userId, depositId).stream().map(
                a -> new UserDepositStep(a.getUserId(), a.getDepositId(), a.getSeq(),
                        a.getCreatedAt(), a.getStatusBefore(), a.getStatusAfter(), a.getInfo(), a.getNotificationId())
        ).toList();
    }


}
