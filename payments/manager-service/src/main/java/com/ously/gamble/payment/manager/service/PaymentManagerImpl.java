package com.ously.gamble.payment.manager.service;

import com.ously.gamble.api.OuslyTransactionException;
import com.ously.gamble.api.crm.CRMUserEvent;
import com.ously.gamble.api.crm.CRMUserUpdateRequest;
import com.ously.gamble.api.features.FeatureConfig;
import com.ously.gamble.api.freespins.BonusHandlerPayoutEvent;
import com.ously.gamble.api.user.UserMessageEvent;
import com.ously.gamble.api.user.UserTransactionService;
import com.ously.gamble.api.user.UserTxRequest;
import com.ously.gamble.api.user.UserTxResponse;
import com.ously.gamble.payment.api.*;
import com.ously.gamble.payment.api.actions.HandlerAction;
import com.ously.gamble.payment.api.actions.ManagerAction;
import com.ously.gamble.payment.api.actions.handler.SendWithdrawAction;
import com.ously.gamble.payment.api.handler.PaymentHandler;
import com.ously.gamble.payment.api.handler.info.PaymentContext;
import com.ously.gamble.payment.manager.builder.HandleNotificationBuilder;
import com.ously.gamble.payment.manager.config.PaymentManagerConfig;
import com.ously.gamble.payment.manager.persistence.idclasses.PaymentPayoutId;
import com.ously.gamble.payment.manager.persistence.model.CallbackStatus;
import com.ously.gamble.payment.manager.persistence.model.PaymentPayout;
import com.ously.gamble.payment.manager.persistence.repository.PaymentCallbackRepository;
import com.ously.gamble.payment.manager.persistence.repository.PaymentDepositRepository;
import com.ously.gamble.payment.manager.persistence.repository.PaymentPayoutRepository;
import com.ously.gamble.payment.manager.service.handlers.UnknownHandler;
import com.ously.gamble.payment.payload.PMContext;
import com.ously.gamble.payment.payload.PMCurrency;
import com.ously.gamble.payment.payload.callback.Callback;
import com.ously.gamble.payment.payload.deposit.DepositDetails;
import com.ously.gamble.payment.payload.deposit.DepositMethod;
import com.ously.gamble.payment.payload.deposit.UserDeposit;
import com.ously.gamble.payment.payload.payout.*;
import com.ously.gamble.payment.payload.withdraw.WithdrawRequest;
import com.ously.gamble.persistence.model.messages.UserMessageContent;
import com.ously.gamble.persistence.model.messages.UserMessageType;
import com.ously.gamble.persistence.model.session.Jurisdiction;
import com.ously.gamble.persistence.model.user.UserTransactionType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ously.gamble.payment.api.handler.info.ActionStatus.ERROR;
import static com.ously.gamble.payment.payload.payout.PayoutRequestType.CONFIRM;

@Service
@ConditionalOnBean(PaymentManagerConfig.class)
public class PaymentManagerImpl implements PaymentManager {
    private static final Collection<PaymentHandler> EMPTYHANDLERSCOLLECTION =
            Collections.emptyList();
    private static final ManagerActionHandler UNKNOWN_ACTION_HANDLER = new UnknownHandler();
    private static final Logger log = LoggerFactory.getLogger(PaymentManagerImpl.class);

    private static final String PO_SECRET = "JustSomeSecret";
    private final List<PaymentHandler> handlers;
    private final Map<Jurisdiction, Collection<PaymentHandler>> handlersByJurisdiction;
    private final Map<String, PaymentHandler> handlersByName;
    private final FeatureConfig fConfig;
    private final DepositManagementService dpMgmtService;
    private final PayoutManagementService poMgmtService;
    private final PaymentCallbackRepository pcRepo;
    private final UserTransactionService usTxService;
    private final PaymentPayoutRepository poRepo;
    private final PaymentManagerGateway pmGW;
    private final Map<Class, ManagerActionHandler<? extends ManagerAction>> actionHandlerMap;
    private final ApplicationEventPublisher eventPublisher;
    private final PayoutApprovalService payoutApprovalService;
    private final PaymentDepositRepository depositRepo;

    public PaymentManagerImpl(List<PaymentHandler> handlers,
                              FeatureConfig fConfig,
                              DepositManagementService dpmg,
                              PayoutManagementService pomg,
                              List<ManagerActionHandler<? extends ManagerAction>> mgrHandlers,
                              PaymentCallbackRepository pcRepo,
                              UserTransactionService utxS,
                              PaymentPayoutRepository poRepo,
                              PaymentManagerGateway pmgw,
                              PaymentDepositRepository depRepo,
                              ApplicationEventPublisher eventPublisher,
                              PayoutApprovalService poApprovalS) {
        this.fConfig = fConfig;
        this.pcRepo = pcRepo;
        this.pmGW = pmgw;
        this.usTxService = utxS;
        this.dpMgmtService = dpmg;
        this.poMgmtService = pomg;
        this.poRepo = poRepo;
        this.handlers = handlers;
        this.eventPublisher = eventPublisher;
        this.payoutApprovalService = poApprovalS;
        this.depositRepo = depRepo;
        this.actionHandlerMap = mgrHandlers.stream().collect(Collectors.toMap(ManagerActionHandler::getActionClass
                , Function.identity()));
        handlersByJurisdiction = new ConcurrentHashMap<>();
        for (var ph : handlers) {
            for (var jd : ph.getInfo().allowedJurisdictions()) {
                var handlersForJurisdiction = handlersByJurisdiction.computeIfAbsent(jd, key -> new ArrayList<>(1));
                handlersForJurisdiction.add(ph);
            }
        }

        handlersByName = handlers.stream().collect(Collectors.toMap(a -> a.getInfo().name(),
                Function.identity()));
    }

    @Override
    public UserPaymentStatistic getPaymentStatisticsForUser(long userId) {
        return depositRepo.getPaymentStatisticForUser(userId);
    }

    @Override
    public Collection<PaymentHandler> getActiveHandlers() {
        return Collections.unmodifiableCollection(handlers);
    }

    @Override
    public Collection<PaymentHandler> getActiveHandlersForJurisdiction(Jurisdiction jd) {
        return handlersByJurisdiction.getOrDefault(jd, Collections.emptyList());
    }

    @Override
    public Page<UserDeposit> getDeposits(Long userId, LocalDate from,
                                         LocalDate to, Pageable pageable) {
        return dpMgmtService.getDepositsForUser(userId, from, to, pageable);
    }

    @Override
    public UserDeposit getDeposit(String depositId) {
        return dpMgmtService.getDeposit(depositId);
    }

    @Override
    public Page<UserPayout> getPayouts(Long userId, LocalDate from,
                                       LocalDate to, Pageable pageable) {
        return poMgmtService.getPayoutsForUser(userId, from, to, pageable);
    }

    @Override
    public List<UserDepositAddress> getUserDepositAddresses(Long userId) {
        List<UserDepositAddress> result = new ArrayList<>(5);
        handlersByJurisdiction.getOrDefault(fConfig.getJurisdiction(), EMPTYHANDLERSCOLLECTION).forEach(handler -> result.addAll(handler.getUserDepositAddresses(userId)));

        return result;
    }

    @Override
    public Collection<DepositMethod> getAvailableDepositMethods() {
        List<DepositMethod> result = new ArrayList<>(5);
        handlersByJurisdiction.getOrDefault(fConfig.getJurisdiction(), EMPTYHANDLERSCOLLECTION).forEach(handler -> result.addAll(handler.getAvailableDepositMethods(fConfig.getJurisdiction())));
        return result;
    }

    @Override
    public PayoutInfo getPayoutInfo(long userId) {
        List<PayoutMethod> result = new ArrayList<>(5);
        handlersByJurisdiction.getOrDefault(fConfig.getJurisdiction(), EMPTYHANDLERSCOLLECTION).forEach(handler -> result.addAll(handler.getAvailablePayoutMethods(fConfig.getJurisdiction())));

        var availablePayoutAmountForUser = poMgmtService.getAvailablePayoutAmountForUser(userId);
        var availablePayoutAmount = availablePayoutAmountForUser.getValue0();
        var openWager = availablePayoutAmountForUser.getValue1();

        return new PayoutInfo(result, openWager, availablePayoutAmount);
    }

    @Override
    public Optional<DepositDetails> getDepositDetails(long userId, String pmntName) {
        try {
            var split = pmntName.split("-");
            var hName = split[0];
            var hMethod = split[1];
            var paymentHandler = handlersByName.get(hName);
            if (paymentHandler == null) {
                return Optional.empty();
            }
            return paymentHandler.getDepositDetailsForUserAndPaymentName(userId, hMethod);
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    @Override
    public Optional<PayoutDetails> getPayoutDetails(Long userId, String pmntName) {
        try {
            var split = pmntName.split("-");
            var hName = split[0];
            var hMethod = split[1];
            var paymentHandler = handlersByName.get(hName);
            if (paymentHandler == null) {
                return Optional.empty();
            }
            return paymentHandler.getPayoutDetailsForUserAndPaymentName(userId, hMethod);
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    @Override
    @Transactional
    public Optional<PayoutResult> requestPayout(long userId, PayoutRequest req) {

        var poDetails = getPayoutDetails(userId, req.payoutMethod()).orElse(null);
        if (poDetails == null) {
            return Optional.empty();
        }
        var requestedPayoutAmount = Objects.requireNonNullElseGet(req.amountToPayout(),
                poDetails::minPayoutAmount);
        var availablePayoutAmountForUser = poMgmtService.getAvailablePayoutAmountForUser(userId);
        var availablePayoutAmount = availablePayoutAmountForUser.getValue0();
        var openWager = availablePayoutAmountForUser.getValue1();
        var selectedPayoutAmount =
                poDetails.minPayoutAmount().max(requestedPayoutAmount).min(availablePayoutAmount);

        // if requested amount higher than available
        if (availablePayoutAmount.subtract(requestedPayoutAmount).signum() < 0) {
            return Optional.of(new PayoutResult(
                    poDetails.minPayoutAmount(),
                    requestedPayoutAmount,
                    availablePayoutAmount,
                    openWager,
                    req.payoutMethod(),
                    req.payoutAddress(),
                    req.payoutTag(),
                    poDetails.rate().multiply(availablePayoutAmount),
                    poDetails.rateValidTo(),
                    PayoutStatus.INSUFFICIENT_BALANCE,
                    null,
                    null,
                    null
            ));
        }

        // if available amount is less than minimum amount
        if (availablePayoutAmount.subtract(poDetails.minPayoutAmount()).signum() < 0) {
            return Optional.of(new PayoutResult(
                    poDetails.minPayoutAmount(),
                    BigDecimal.ZERO,
                    availablePayoutAmount,
                    openWager,
                    req.payoutMethod(),
                    req.payoutAddress(),
                    req.payoutTag(),
                    poDetails.rate().multiply(availablePayoutAmount),
                    poDetails.rateValidTo(),
                    PayoutStatus.INSUFFICIENT_BALANCE,
                    null,
                    null,
                    null
            ));
        }

        // if selected amount is less than minimum needed amount for method, return with
        // AMOUNT_TO_LOW status
        if (requestedPayoutAmount.subtract(poDetails.minPayoutAmount()).signum() < 0) {
            return Optional.of(new PayoutResult(
                    poDetails.minPayoutAmount(),
                    requestedPayoutAmount,
                    availablePayoutAmount,
                    openWager,
                    req.payoutMethod(),
                    req.payoutAddress(),
                    req.payoutTag(),
                    poDetails.rate().multiply(availablePayoutAmount),
                    poDetails.rateValidTo(),
                    PayoutStatus.AMOUNT_TO_LOW,
                    null,
                    null,
                    null
            ));
        }

        // Check address and tag
        // TODO: validate address (and tag if needed)
        if (Objects.requireNonNullElse(req.payoutAddress(), "").isEmpty()) {
            return Optional.of(new PayoutResult(
                    poDetails.minPayoutAmount(),
                    requestedPayoutAmount,
                    availablePayoutAmount,
                    openWager,
                    req.payoutMethod(),
                    req.payoutAddress(),
                    req.payoutTag(),
                    poDetails.rate().multiply(availablePayoutAmount),
                    poDetails.rateValidTo(),
                    PayoutStatus.ADDRESS_INVALID,
                    null,
                    null,
                    null
            ));

        }
        // Confirm
        if (req.type() == CONFIRM) {
            return confirmPayoutRequest(userId, req, poDetails, availablePayoutAmount, openWager);
        }

        // REFRESH
        return Optional.of(new PayoutResult(
                poDetails.minPayoutAmount(),
                selectedPayoutAmount,
                availablePayoutAmount,
                openWager,
                req.payoutMethod(),
                req.payoutAddress(),
                req.payoutTag(),
                poDetails.rate().multiply(selectedPayoutAmount),
                poDetails.rateValidTo(),
                PayoutStatus.CONFIRM,
                createConfirmationTag(selectedPayoutAmount, req.payoutMethod(), req.payoutAddress(),
                        req.payoutTag(), poDetails.rateValidTo().getEpochSecond(), PO_SECRET, userId),
                null,
                payoutApprovalService.checkApprovalStatus(userId, selectedPayoutAmount, true)
        ));
    }


    @Override
    public void performExecutePayout(PayoutExecuteRequest req) {
        var byId = poRepo.findById(new PaymentPayoutId(req.userId(), req.poId()));
        if (byId.isEmpty()) {
            log.error("Cannot find payout for '{}'", req);
            return;
        }
        var payout = byId.get();
        // Check status
        if (payout.getStatus() != PaymentStatus.CREATED && payout.getStatus() != PaymentStatus.APPROVED) {
            log.error("Payout for request '{}' is not in status CREATED/APPROVED. Doing nothing, " +
                    "possible " +
                    "dupl. message", req);
            return;
        }

        // Create handler
        var paymentHandler = handlersByName.get(payout.getHandler());
        if (paymentHandler == null) {
            log.error("Payout->cannot find handler for request '{}'", req);
            return;

        }
        // avoid sending twice
        payout.setStatus(PaymentStatus.SENDING);
        payout = poRepo.saveAndFlush(payout);

        // send withdraw request
        var ctx = new PaymentContext();
        var wdReq = new WithdrawRequest(
                payout.getUserId() + ":" + payout.getPayoutId(),
                PMCurrency.valueOf(payout.getCurrency()),
                PMCurrency.valueOf(payout.getCurrencyTo()),
                payout.getAmount(),
                payout.getAddress(),
                payout.getTag()
        );
        var swa = new SendWithdrawAction(
                wdReq, new PMContext(payout.getHandler(), payout.getUserId(), payout.getPayoutId())
        );
        paymentHandler.performActions(Collections.singletonList(swa), ctx);

        // check status

        if (ctx.getCurrentStatus() == ERROR) {
            var lal = ctx.getLastActionLog();
            poMgmtService.changePayoutStatus(req.userId(), req.poId(), PaymentStatus.INTERNAL_ERROR
                    , null, "Error while trying to send payout request", (lal == null) ? "" :
                            lal.toString());
        } else {
            log.info("Sent payout request succesfully!!");
            sendCRMEvent(PaymentStatus.SENDING, req.userId(), payout.getAmount(), payout.getCurrencyTo(), payout.getId().getPayoutId(), "", "");
        }

        poRepo.saveAndFlush(payout);

    }

    @Transactional
    @Override
    public void performPayoutApproval(PayoutApprovalRequest req1, Long id) {

        PayoutApprovalRequest req = new PayoutApprovalRequest(req1.userId(), req1.payoutId(), req1.reason(), req1.reasonDescription(), id);


        // 1. find payout
        var optPayout = poRepo.findById(new PaymentPayoutId(req.userId(),
                req.payoutId()));
        if (optPayout.isEmpty()) {
            log.warn("Got payout approval request for uid/poid->{}/'{}' but that payout does not " +
                            "exist!",
                    req.userId(), req.payoutId());
            return;
        }

        var payout = optPayout.get();
        if (payout.getStatus() != PaymentStatus.PENDING) {
            log.warn("Got payout approval request for uid/poid->{}/'{}' but that payout is not in" +
                    " status PENDING.", req.userId(), req.payoutId());
            return;
        }
        log.info("Setting status to APPROVED");
        // Adding payout step
        poMgmtService.changePayoutStatus(req.userId(),
                req.payoutId(), PaymentStatus.APPROVED, req.adminId(), req.reason().name(),
                req.reasonDescription());

        // Inform user
        Map<String, String> vars = new HashMap<>(6);
        vars.put("payoutCurrency", payout.getCurrencyTo());
        vars.put("walletAmount", payout.getAmount().setScale(2, RoundingMode.DOWN).toPlainString());
        vars.put("payoutType", payout.getHandler());
        vars.put("payoutAddress", payout.getAddress());
        vars.put("payoutTag", payout.getTag());
        vars.put("payoutId", payout.getPayoutId());
        vars.put("payoutAmount", payout.getAmount().divide(payout.getConversionRate(), 8, RoundingMode.DOWN).toPlainString());
        eventPublisher.publishEvent(new UserMessageEvent(req.userId(), true,
                optPayout.get().getPayoutId(),
                new UserMessageContent(UserMessageType.PAYOUT_SENT, vars, Collections.emptyMap())));

        // Execute payout
        log.info("Executing payout");
        performExecutePayout(new PayoutExecuteRequest(
                payout.getUserId(), payout.getPayoutId(), payout.getAddress(), payout.getTag()
        ));


    }

    @Transactional
    @Override
    public void performPayoutAbort(PayoutAbortRequest req1, Long id) {

        PayoutAbortRequest req = new PayoutAbortRequest(req1.userId(), req1.payoutId(), req1.reason(), req1.reasonDescription(), id);
        // 1. find payout
        var optPayout = poRepo.findById(new PaymentPayoutId(req.userId(),
                req.payoutId()));
        if (optPayout.isEmpty()) {
            log.warn("Got payout abort request for uid/poid->{}/'{}' but that payout does not " +
                            "exist!",
                    req.userId(), req.payoutId());
            return;
        }

        var payout = optPayout.get();
        if (payout.getStatus() == PaymentStatus.SUCCESS) {
            log.warn("Got payout abort request for uid/poid->{}/'{}' but that payout has already " +
                    "been sent and cant be aborted anymore.", req.userId(), req.payoutId());
            return;
        }
        if (payout.getStatus() == PaymentStatus.SENDING) {
            log.warn("Got payout abort request for uid/poid->{}/'{}' but that payout is currently" +
                            "in sending process and cannot be aborted currently.", req.userId(),
                    req.payoutId());
            return;
        }

        if (payout.getStatus() == PaymentStatus.ABORTED) {
            log.warn("Got payout abort request for uid/poid->{}/'{}' but that payout has been " +
                            "aborted already.", req.userId(),
                    req.payoutId());
            return;
        }

        // Now check if there is a user transaction attached, rollback this and set status to
        // aborted
        var utxId = payout.getInternalTransactionId();
        if (utxId != null) {
            var utx = usTxService.findTransaction(req.userId(), utxId);
            if (utx == null) {
                log.warn("Cannot find userTransaction userId/txId -> {}/{}", req.userId(), utxId);
                return;
            }
            if (utx.getCancelId() != null && utx.getCancelId() > 0L) {
                log.info("UserTransaction userId/txId -> {}/{} is already cancelled, setting " +
                        "payout to ABORTED.", req.userId(), utxId);
            } else {
                log.info("Cancelling userTransaction userId/txId -> {}/{}", req.userId(), utxId);
                usTxService.rollbackTransaction(utx);
            }
        } else {
            log.warn("No userTransaction set on payout ({}/{}), setting status to ABORTED without" +
                            " rollback",
                    req.userId(), utxId);
        }

        // Adding payout step
        poMgmtService.changePayoutStatus(req.userId(),
                req.payoutId(), PaymentStatus.ABORTED, req.adminId(), req.reason().name(),
                req.reasonDescription());


        sendCRMEvent(PaymentStatus.ABORTED, req.userId(), payout.getAmount(), payout.getCurrencyTo(), payout.getId().getPayoutId(), req.reason().name(), req.reasonDescription());

    }

    @Override
    @Transactional
    public Optional<UserPayout> getUserPayout(long userId, String payoutId) {
        var byId = poRepo.findById(new PaymentPayoutId(userId, payoutId));
        if (byId.isPresent()) {

            return byId.map(a -> new UserPayout(
                    a.getPayoutId(),
                    a.getUserId(),
                    a.getStatus().name(),
                    PMCurrency.valueOf(a.getCurrency()),
                    a.getAmount(),
                    PMCurrency.valueOf(a.getCurrencyTo()),
                    a.getAmount().divide(a.getConversionRate(), 8, RoundingMode.HALF_UP),
                    a.getFee(),
                    BigDecimal.ZERO,
                    a.getCreatedAt(),
                    null,
                    "",
                    a.getAddress(),
                    a.getTag()));
        }
        return Optional.empty();
    }

    private static String createConfirmationTag(BigDecimal selectedPayoutAmount, String payoutMethod,
                                                String payoutAddress, String payoutTag, long epochSecond,
                                                String lSecret, long userId) {
        var sigBase =
                selectedPayoutAmount.toPlainString() + payoutMethod + payoutAddress + payoutTag + (epochSecond / 100) + lSecret + userId;
        var cTag = DigestUtils.md5DigestAsHex(sigBase.getBytes(StandardCharsets.UTF_8));
        log.info("confirmTag:{} for {},{},{},{},{},{}", cTag,
                selectedPayoutAmount.toPlainString(), payoutMethod, payoutAddress, payoutTag,
                epochSecond, userId);
        return cTag;
    }

    private Optional<PayoutResult> confirmPayoutRequest(long userId, PayoutRequest req,
                                                        PayoutDetails poDetails,
                                                        BigDecimal availablePayoutAmount,
                                                        BigDecimal openWager) {
        // Check tag
        var preCreateApproval = payoutApprovalService.checkApprovalStatus(userId,
                req.amountToPayout(), true);
        log.info("PraApproval:{}", preCreateApproval.name());
        if (
                !createConfirmationTag(req.amountToPayout(), req.payoutMethod(), req.payoutAddress(),
                        req.payoutTag(), poDetails.rateValidTo().getEpochSecond(), PO_SECRET, userId).equals(req.confirmationTag())
                        ||
                        (preCreateApproval == ApprovalStatus.DECLINED_BLOCK || preCreateApproval == ApprovalStatus.DECLINED_UNKNOWN)
        ) {
            return Optional.of(new PayoutResult(
                    poDetails.minPayoutAmount(),
                    req.amountToPayout(),
                    availablePayoutAmount,
                    openWager,
                    req.payoutMethod(),
                    req.payoutAddress(),
                    req.payoutTag(),
                    poDetails.rate().multiply(req.amountToPayout()),
                    poDetails.rateValidTo(),
                    PayoutStatus.CONFIRM,
                    createConfirmationTag(req.amountToPayout(), req.payoutMethod(), req.payoutAddress(),
                            req.payoutTag(), poDetails.rateValidTo().getEpochSecond(), PO_SECRET, userId),
                    null,
                    preCreateApproval
            ));
        }

        // Seems legit, now try to create and send withdraw
        // compile proper withdraw record, send to handler, store payout item.
        // take care: it could be sending withdraw is successful, but then storage of payout item
        // fails (for whatever reason). Evtl. just send withdraw to queue after payout has been
        // generated: so we do payout entity creation first and store msg in rabbit. Then
        // listener reads msg and sends out payout request and marks payout entity. User will
        // receive a notification on any status updates.

        var poId = DigestUtils.md5DigestAsHex((req.confirmationTag() + ':' + userId).getBytes(StandardCharsets.UTF_8));

        // Create deduction of Amount from wallet

        var txReq = new UserTxRequest();
        txReq.setType(UserTransactionType.PAYOUT_RESERVE);
        txReq.setUserId(userId);
        txReq.setAddWager(BigDecimal.ZERO);
        txReq.setWithdraw(req.amountToPayout());
        txReq.setTxRef("PAYOUT:" + poId);
        txReq.setDescription(StringUtils.abbreviate("PO:" + req.payoutMethod() + ':' + req.payoutAddress() + ':' + Objects.requireNonNullElse(req.payoutTag(), ""), 98));
        txReq.setCredit(BigDecimal.ZERO);
        txReq.setPiggyPossible(false);
        txReq.setStorePrices(false);

        UserTxResponse userTxResponse;
        try {
            userTxResponse = usTxService.addTransaction(txReq);
            eventPublisher.publishEvent(new BonusHandlerPayoutEvent(poId, userId, req.amountToPayout()));
            eventPublisher.publishEvent(new CRMUserUpdateRequest(userId));
        } catch (OuslyTransactionException e) {
            log.error("Error deducting payout amount({}) from user:{}", req.amountToPayout(),
                    userId, e);
            return Optional.empty();
        }

        // The foreign id is cpuid:userId:payoutId

        // Create payout entity (incl. userTransaction link)

        var payout = new PaymentPayout();
        var crInstant = Instant.now();
        payout.setPayoutId(poId);
        payout.setUserId(userId);
        payout.setAmount(req.amountToPayout());
        payout.setCreatedAt(crInstant);
        payout.setUpdatedAt(crInstant);
        payout.setFee(BigDecimal.ZERO);
        payout.setCurrency("USDTE");
        payout.setCurrencyTo(getCurrencyFromMethod(req.payoutMethod()));
        payout.setConversionRate(BigDecimal.ONE.divide(poDetails.rate(), 8, RoundingMode.HALF_UP));
        payout.setHandler(poDetails.handler());
        payout.setStatus(PaymentStatus.CREATED);
        payout.setAddress(req.payoutAddress());
        payout.setTag(Objects.requireNonNullElse(req.payoutTag(), ""));
        payout.setVersion(1);
        payout.setInternalTransactionId(userTxResponse.getTxId());
        payout = poRepo.saveAndFlush(payout);

        // send payout request
        // userId, poId, payout.getAddress, payout.getTag
        var approvalStatus = payoutApprovalService.checkApprovalStatus(userId, req.amountToPayout(), false);

        if (approvalStatus == ApprovalStatus.APPROVED) {
            sendCRMEvent(PaymentStatus.CREATED, userId, payout.getAmount(), payout.getCurrencyTo(), poId, "limitchecks ok", "");
            poMgmtService.changePayoutStatus(userId, payout.getPayoutId(),
                    PaymentStatus.APPROVED, null, null, "limitchecks on payout creation " +
                            "resulted in APPROVAL");
        } else {
            sendCRMEvent(PaymentStatus.PENDING, userId, payout.getAmount(), payout.getCurrencyTo(), poId, "limitchecks resulted in " + approvalStatus.name(), "");
            poMgmtService.changePayoutStatus(userId, payout.getPayoutId(),
                    PaymentStatus.PENDING, null, null, "limitchecks on payout creation resulted " +
                            "in " + approvalStatus.name());
        }

        // EXECUTE PAYOUT if APPROVED
        if (approvalStatus == ApprovalStatus.APPROVED) {
            var poExecReq = new PayoutExecuteRequest(
                    payout.getUserId(),
                    payout.getPayoutId(),
                    payout.getAddress(),
                    payout.getTag()
            );
            pmGW.executePayout(poExecReq);
        }

        var poResult = new PayoutResult(
                poDetails.minPayoutAmount(),
                req.amountToPayout(),
                availablePayoutAmount,
                openWager,
                req.payoutMethod(),
                req.payoutAddress(),
                req.payoutTag(),
                poDetails.rate().multiply(req.amountToPayout()),
                poDetails.rateValidTo(),
                PayoutStatus.CREATED,
                null,
                payout.getPayoutId(),
                approvalStatus
        );
        // Message: "payout requested"
        sendPayoutCreatedMessage(userId, poResult, payout.getCurrencyTo());
        return Optional.of(poResult);
    }

    private void sendPayoutCreatedMessage(long userId, PayoutResult poResult, String payoutCurrency) {
        Map<String, String> vars = new HashMap<>(6);

        vars.put("walletAmount", poResult.amountToPayout().setScale(2, RoundingMode.DOWN).toPlainString());
        vars.put("payoutType", poResult.payoutMethod());
        vars.put("payoutAddress", poResult.payoutAddress());
        vars.put("payoutTag", poResult.payoutTag());
        vars.put("payoutId", poResult.payoutId());
        vars.put("payoutAmount", poResult.targetAmount().setScale(8, RoundingMode.DOWN).toPlainString());
        vars.put("payoutCurrency", payoutCurrency);

        switch (poResult.approvalStatus()) {
            case APPROVED -> {
                eventPublisher.publishEvent(new UserMessageEvent(userId, true, poResult.payoutId(), new UserMessageContent(UserMessageType.PAYOUT_SENT, vars, Collections.emptyMap())));
            }
            case DECLINED_BLOCK, DECLINED_UNKNOWN -> eventPublisher.publishEvent(new UserMessageEvent(userId, true,
                    poResult.payoutId(), new UserMessageContent(UserMessageType.PAYOUT_CONTACT_SUPPORT, vars, Collections.emptyMap())));
            default -> {
                eventPublisher.publishEvent(new UserMessageEvent(userId, true,
                        poResult.payoutId(), new UserMessageContent(UserMessageType.PAYOUT_CREATED, vars,
                        Collections.emptyMap())));
            }
        }

    }

    private static String getCurrencyFromMethod(String payoutMethod) {
        return payoutMethod.split("-")[1];
    }

    @SuppressWarnings("unchecked")
    @Override
    public void handleCallback(Callback pc) {
        var paymentHandler = handlersByName.get(pc.getHandler());
        List<HandlerAction> actions = new ArrayList<>(1);
        actions.add(HandleNotificationBuilder.notificationBuilder(pc).build());
        var ctx = new PaymentContext();
        log.info("Send {} actions to handler '{}'", actions.size(), paymentHandler.getInfo());
        var actionResults = paymentHandler.performActions(actions, ctx);
        log.info("Got {} actions from handler. Actions='{}'",
                actionResults.size(), actionResults);
        for (var mgrA : actionResults) {
            var actionHandler = actionHandlerMap.getOrDefault(mgrA.getClass(),
                    UNKNOWN_ACTION_HANDLER);
            try {
                actionHandler.performManagerAction(mgrA, ctx, this);
            } catch (Exception e) {
                log.error("Error while trying to perform Action {}", mgrA.getType(), e);
            }
        }

        // set state to HANDLED (or ERROR)
        var optCallback = pcRepo.findById(pc.getMd5());
        if (ctx.getCurrentStatus() != ERROR) {
            optCallback.ifPresent(a -> a.setStatus(CallbackStatus.HANDLED));
        } else {
            optCallback.ifPresent(a -> a.setStatus(CallbackStatus.ERROR));
        }

    }


    public void sendCRMEvent(PaymentStatus status, long userId, BigDecimal amount, String currency, String payoutId, String reason, String description) {

        switch (status) {
            case CREATED -> eventPublisher.publishEvent(
                    new CRMUserEvent(userId, "MONEY_WITHDRAW_REQUESTED", "amount", amount, "currency", currency, "payment_agent", "CP_AP", "payout_id", payoutId
                    ));
            case ABORTED -> eventPublisher.publishEvent(
                    new CRMUserEvent(userId, "MONEY_WITHDRAW_CANCELLED", "amount", amount, "currency", currency, "payment_agent", "CP_AP", "payout_id", payoutId,
                            "reason", reason, "description", description));
            case SENDING -> eventPublisher.publishEvent(
                    new CRMUserEvent(userId, "MONEY_WITHDRAW_SENT", "amount", amount, "currency", currency, "payment_agent", "CP_AP", "payout_id", payoutId
                    ));
            case PENDING, SUPPORT -> eventPublisher.publishEvent(
                    new CRMUserEvent(userId, "MONEY_WITHDRAW_CHECK", "amount", amount, "currency", currency, "payment_agent", "CP_AP", "payout_id", payoutId,
                            "reason", reason, "description", description));
        }
    }


}
