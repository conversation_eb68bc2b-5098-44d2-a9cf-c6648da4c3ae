CREATE TABLE  if not exists `payment_callback`
(
    `md5`           VARCHAR(32) NOT NULL,
    `handler`       VA<PERSON><PERSON><PERSON>(16) NOT NULL,
    `name`          VARCHAR(64) NOT NULL,
    `status`          VARCHAR(16) NOT NULL DEFAULT 'RECEIVED',
    `body`  JSON        NOT NULL,
    `received_at`   TIMESTAMP   NOT NULL,
    `queued_at`     TIMESTAMP   NULL,
    `requeue_at`    TIMESTAMP   NULL,
    `requeue_count` smallint    not null DEFAULT 0,
    `sig_checked`   bit(1)      not null DEFAULT false,
    `info`          VARCHAR(255)         DEFAULT NULL,
    `version`       smallint    not null default 1,
    PRIMARY KEY (`md5`),
    INDEX `idx_pmnt_callbk_requeue_at` (`requeue_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
