plugins {
    id 'java-library'
}


configurations {
    jaxb
}

tasks.register('genJaxb') {
    System.setProperty('javax.xml.accessExternalSchema', 'all')
    doLast {
        def jaxbTargetDir = file("$buildDir/generated/src/main/java")
        // ext.classesDir = "${buildDir}/classes/jaxb"

        if (!jaxbTargetDir.exists()) {
            jaxbTargetDir.mkdirs()
        }

        ant.taskdef(name: 'xjc', classname: 'com.sun.tools.xjc.XJCTask', classpath: configurations.jaxb.asPath)

        ant.xjc(
                destdir: "${jaxbTargetDir}",
                schema: "${projectDir}/src/main/resources/edict/edict-generated-wallet.wsdl",
                //              binding: "${projectDir}/src/main/resources/edict/edict-generated-wallet-binding.xml",
                //              catalog: "${projectDir}/src/main/resources/xsd/catalog.xml",
                package: 'com.ously.gamble.bridge.edict.model.wallet',
                language: 'WSDL',
                removeOldOutput: 'yes', extension: 'true'
        )
                {
                    arg(line: '-wsdl')
                }


        ant.xjc(
                destdir: "${jaxbTargetDir}",
                schema: "${projectDir}/src/main/resources/edict/edict-generated-authorize.wsdl",
                //              binding: "${projectDir}/src/main/resources/edict/edict-generated-wallet-binding.xml",
                //              catalog: "${projectDir}/src/main/resources/xsd/catalog.xml",
                package: 'com.ously.gamble.bridge.edict.model.authorize',
                language: 'WSDL',
                removeOldOutput: 'yes', extension: 'true'
        )
                {
                    arg(line: '-wsdl')
                }

        ant.xjc(
                destdir: "${jaxbTargetDir}",
                schema: "${projectDir}/src/main/resources/edict/edict-generated-gamesession.wsdl",
                //              binding: "${projectDir}/src/main/resources/edict/edict-generated-wallet-binding.xml",
                //              catalog: "${projectDir}/src/main/resources/xsd/catalog.xml",
                package: 'com.ously.gamble.bridge.edict.model.gamesession',
                language: 'WSDL',
                removeOldOutput: 'yes', extension: 'true'
        )
                {
                    arg(line: '-wsdl')
                }
    }
}

test {
    useJUnitPlatform()
    systemProperty 'junit.jupiter.extensions.autodetection.enabled', 'true'
    systemProperty 'flyway.enabled', 'false'
}

dependencies {

    implementation project(':api')
    implementation project(':configuration')
    implementation project(':services')
    implementation project(':persistence')

    implementation "org.springframework.amqp:spring-rabbit"

    implementation "org.springframework.security:spring-security-web"

    implementation "org.springframework.boot:spring-boot-starter-web-services:${springBootVersion}", {
        exclude group: "org.springframework.boot", module: "spring-boot-starter-tomcat"
    }
    implementation 'wsdl4j:wsdl4j'
    jaxb("org.glassfish.jaxb:jaxb-xjc")

    implementation "org.springframework.boot:spring-boot-starter-freemarker:${springBootVersion}"

    implementation("com.fasterxml.jackson.core:jackson-annotations")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jdk8")
    implementation("com.fasterxml.jackson.core:jackson-databind")

    implementation "net.javacrumbs.shedlock:shedlock-spring:${shedlockSpringVersion}"


    //
    implementation 'org.springframework.ws:spring-ws-core'

    // security
    implementation 'io.jsonwebtoken:jjwt:0.9.1'
    implementation group: 'javax.xml.bind', name: 'jaxb-api', version: '2.1'


//            // Uncomment the next line if you want to use RSASSA-PSS (PS256, PS384, PS512) algorithms:
//            //'org.bouncycastle:bcprov-jdk15on:1.60',
//            'io.jsonwebtoken:jjwt-jackson:0.11.5' // or 'io.jsonwebtoken:jjwt-gson:0.11.2' for gson
//    implementation group: 'org.bouncycastle', name: 'bcpkix-jdk15on', version: '1.70'


    implementation "commons-io:commons-io:2.11.0"
    implementation 'org.apache.commons:commons-collections4:4.4'
    implementation "commons-codec:commons-codec:1.15"

    implementation 'org.apache.httpcomponents.client5:httpclient5:5.2.1'

    // Jakarta Bean Validation
    implementation 'org.hibernate.validator:hibernate-validator:8.0.1.Final'
    implementation 'org.glassfish:jakarta.el:4.0.2'


}


sourceSets {
    main {
        java {
            srcDir 'src/main/java'
            srcDir 'build/generated/src/main/java'
        }
    }
}


project.afterEvaluate {
    // ====  Ensure sources are generated before compiling  =====
    // needed for bridge!!
    compileJava.dependsOn("genJaxb")
}